{"version": "4", "specifiers": {"npm:octokit@*": "4.1.2_@octokit+core@6.1.4"}, "npm": {"@octokit/app@15.1.5_@octokit+core@6.1.4": {"integrity": "sha512-6cxLT9U8x7GGQ7lNWsKtFr4ccg9oLkGvowk373sX9HvX5U37kql5d55SzaQUxPE8PwgX2cqkzDm5NF5aPKevqg==", "dependencies": ["@octokit/auth-app", "@octokit/auth-unauthenticated", "@octokit/core", "@octokit/oauth-app", "@octokit/plugin-paginate-rest", "@octokit/types", "@octokit/webhooks"]}, "@octokit/auth-app@7.1.5": {"integrity": "sha512-boklS4E6LpbA3nRx+SU2fRKRGZJdOGoSZne/i3Y0B5rfHOcGwFgcXrwDLdtbv4igfDSnAkZaoNBv1GYjPDKRNw==", "dependencies": ["@octokit/auth-oauth-app", "@octokit/auth-oauth-user", "@octokit/request", "@octokit/request-error", "@octokit/types", "toad-cache", "universal-github-app-jwt", "universal-user-agent"]}, "@octokit/auth-oauth-app@8.1.3": {"integrity": "sha512-4e6OjVe5rZ8yBe8w7byBjpKtSXFuro7gqeGAAZc7QYltOF8wB93rJl2FE0a4U1Mt88xxPv/mS+25/0DuLk0Ewg==", "dependencies": ["@octokit/auth-oauth-device", "@octokit/auth-oauth-user", "@octokit/request", "@octokit/types", "universal-user-agent"]}, "@octokit/auth-oauth-device@7.1.3": {"integrity": "sha512-BECO/N4B/Uikj0w3GCvjf/odMujtYTP3q82BJSjxC2J3rxTEiZIJ+z2xnRlDb0IE9dQSaTgRqUPVOieSbFcVzg==", "dependencies": ["@octokit/oauth-methods", "@octokit/request", "@octokit/types", "universal-user-agent"]}, "@octokit/auth-oauth-user@5.1.3": {"integrity": "sha512-zNPByPn9K7TC+OOHKGxU+MxrE9SZAN11UHYEFLsK2NRn3akJN2LHRl85q+Eypr3tuB2GrKx3rfj2phJdkYCvzw==", "dependencies": ["@octokit/auth-oauth-device", "@octokit/oauth-methods", "@octokit/request", "@octokit/types", "universal-user-agent"]}, "@octokit/auth-token@5.1.2": {"integrity": "sha512-JcQDsBdg49Yky2w2ld20IHAlwr8d/d8N6NiOXbtuoPCqzbsiJgF633mVUw3x4mo0H5ypataQIX7SFu3yy44Mpw=="}, "@octokit/auth-unauthenticated@6.1.2": {"integrity": "sha512-07DlUGcz/AAVdzu3EYfi/dOyMSHp9YsOxPl/MPmtlVXWiD//GlV8HgZsPhud94DEyx+RfrW0wSl46Lx+AWbOlg==", "dependencies": ["@octokit/request-error", "@octokit/types"]}, "@octokit/core@6.1.4": {"integrity": "sha512-lAS9k7d6I0MPN+gb9bKDt7X8SdxknYqAMh44S5L+lNqIN2NuV8nvv3g8rPp7MuRxcOpxpUIATWprO0C34a8Qmg==", "dependencies": ["@octokit/auth-token", "@octokit/graphql", "@octokit/request", "@octokit/request-error", "@octokit/types", "before-after-hook", "universal-user-agent"]}, "@octokit/endpoint@10.1.3": {"integrity": "sha512-nBRBMpKPhQUxCsQQeW+rCJ/OPSMcj3g0nfHn01zGYZXuNDvvXudF/TYY6APj5THlurerpFN4a/dQAIAaM6BYhA==", "dependencies": ["@octokit/types", "universal-user-agent"]}, "@octokit/graphql@8.2.1": {"integrity": "sha512-n57hXtOoHrhwTWdvhVkdJHdhTv0JstjDbDRhJfwIRNfFqmSo1DaK/mD2syoNUoLCyqSjBpGAKOG0BuwF392slw==", "dependencies": ["@octokit/request", "@octokit/types", "universal-user-agent"]}, "@octokit/oauth-app@7.1.6": {"integrity": "sha512-OMcMzY2WFARg80oJNFwWbY51TBUfLH4JGTy119cqiDawSFXSIBujxmpXiKbGWQlvfn0CxE6f7/+c6+Kr5hI2YA==", "dependencies": ["@octokit/auth-oauth-app", "@octokit/auth-oauth-user", "@octokit/auth-unauthenticated", "@octokit/core", "@octokit/oauth-authorization-url", "@octokit/oauth-methods", "@types/aws-lambda", "universal-user-agent"]}, "@octokit/oauth-authorization-url@7.1.1": {"integrity": "sha512-ooXV8GBSabSWyhLUowlMIVd9l1s2nsOGQdlP2SQ4LnkEsGXzeCvbSbCPdZThXhEFzleGPwbapT0Sb+YhXRyjCA=="}, "@octokit/oauth-methods@5.1.4": {"integrity": "sha512-Jc/ycnePClOvO1WL7tlC+TRxOFtyJBGuTDsL4dzXNiVZvzZdrPuNw7zHI3qJSUX2n6RLXE5L0SkFmYyNaVUFoQ==", "dependencies": ["@octokit/oauth-authorization-url", "@octokit/request", "@octokit/request-error", "@octokit/types"]}, "@octokit/openapi-types@23.0.1": {"integrity": "sha512-izFjMJ1sir0jn0ldEKhZ7xegCTj/ObmEDlEfpFrx4k/JyZSMRHbO3/rBwgE7f3m2DHt+RrNGIVw4wSmwnm3t/g=="}, "@octokit/openapi-webhooks-types@10.1.1": {"integrity": "sha512-qBfqQVIDQaCFeGCofXieJDwvXcGgDn17+UwZ6WW6lfEvGYGreLFzTiaz9xjet9Us4zDf8iasoW3ixUj/R5lMhA=="}, "@octokit/plugin-paginate-graphql@5.2.4_@octokit+core@6.1.4": {"integrity": "sha512-pLZES1jWaOynXKHOqdnwZ5ULeVR6tVVCMm+AUbp0htdcyXDU95WbkYdU4R2ej1wKj5Tu94Mee2Ne0PjPO9cCyA==", "dependencies": ["@octokit/core"]}, "@octokit/plugin-paginate-rest@11.4.3_@octokit+core@6.1.4": {"integrity": "sha512-tBXaAbXkqVJlRoA/zQVe9mUdb8rScmivqtpv3ovsC5xhje/a+NOCivs7eUhWBwCApJVsR4G5HMeaLbq7PxqZGA==", "dependencies": ["@octokit/core", "@octokit/types"]}, "@octokit/plugin-rest-endpoint-methods@13.3.1_@octokit+core@6.1.4": {"integrity": "sha512-o8uOBdsyR+WR8MK9Cco8dCgvG13H1RlM1nWnK/W7TEACQBFux/vPREgKucxUfuDQ5yi1T3hGf4C5ZmZXAERgwQ==", "dependencies": ["@octokit/core", "@octokit/types"]}, "@octokit/plugin-retry@7.1.4_@octokit+core@6.1.4": {"integrity": "sha512-7AIP4p9TttKN7ctygG4BtR7rrB0anZqoU9ThXFk8nETqIfvgPUANTSYHqWYknK7W3isw59LpZeLI8pcEwiJdRg==", "dependencies": ["@octokit/core", "@octokit/request-error", "@octokit/types", "bottleneck"]}, "@octokit/plugin-throttling@9.4.0_@octokit+core@6.1.4": {"integrity": "sha512-IOlXxXhZA4Z3m0EEYtrrACkuHiArHLZ3CvqWwOez/pURNqRuwfoFlTPbN5Muf28pzFuztxPyiUiNwz8KctdZaQ==", "dependencies": ["@octokit/core", "@octokit/types", "bottleneck"]}, "@octokit/request-error@6.1.7": {"integrity": "sha512-69NIppAwaauwZv6aOzb+VVLwt+0havz9GT5YplkeJv7fG7a40qpLt/yZKyiDxAhgz0EtgNdNcb96Z0u+Zyuy2g==", "dependencies": ["@octokit/types"]}, "@octokit/request@9.2.2": {"integrity": "sha512-dZl0ZHx6gOQGcffgm1/Sf6JfEpmh34v3Af2Uci02vzUYz6qEN6zepoRtmybWXIGXFIK8K9ylE3b+duCWqhArtg==", "dependencies": ["@octokit/endpoint", "@octokit/request-error", "@octokit/types", "fast-content-type-parse", "universal-user-agent"]}, "@octokit/types@13.8.0": {"integrity": "sha512-x7DjTIbEpEWXK99DMd01QfWy0hd5h4EN+Q7shkdKds3otGQP+oWE/y0A76i1OvH9fygo4ddvNf7ZvF0t78P98A==", "dependencies": ["@octokit/openapi-types"]}, "@octokit/webhooks-methods@5.1.1": {"integrity": "sha512-NGlEHZDseJTCj8TMMFehzwa9g7On4KJMPVHDSrHxCQumL6uSQR8wIkP/qesv52fXqV1BPf4pTxwtS31ldAt9Xg=="}, "@octokit/webhooks@13.7.4": {"integrity": "sha512-f386XyLTieQbgKPKS6ZMlH4dq8eLsxNddwofiKRZCq0bZ2gikoFwMD99K6l1oAwqe/KZNzrEziGicRgnzplplQ==", "dependencies": ["@octokit/openapi-webhooks-types", "@octokit/request-error", "@octokit/webhooks-methods"]}, "@types/aws-lambda@8.10.147": {"integrity": "sha512-nD0Z9fNIZcxYX5Mai2CTmFD7wX7UldCkW2ezCF8D1T5hdiLsnTWDGRpfRYntU6VjTdLQjOvyszru7I1c1oCQew=="}, "before-after-hook@3.0.2": {"integrity": "sha512-Nik3Sc0ncrMK4UUdXQmAnRtzmNQTAAXmXIopizwZ1W1t8QmfJj+zL4OA2I7XPTPW5z5TDqv4hRo/JzouDJnX3A=="}, "bottleneck@2.19.5": {"integrity": "sha512-VHiNCbI1lKdl44tGrhNfU3lup0Tj/ZBMJB5/2ZbNXRCPuRCO7ed2mgcK4r17y+KB2EfuYuRaVlwNbAeaWGSpbw=="}, "fast-content-type-parse@2.0.1": {"integrity": "sha512-nGqtvLrj5w0naR6tDPfB4cUmYCqouzyQiz6C5y/LtcDllJdrcc6WaWW6iXyIIOErTa/XRybj28aasdn4LkVk6Q=="}, "octokit@4.1.2_@octokit+core@6.1.4": {"integrity": "sha512-0kcTxJOK3yQrJsRb8wKa28hlTze4QOz4sLuUnfXXnhboDhFKgv8LxS86tFwbsafDW9JZ08ByuVAE8kQbYJIZkA==", "dependencies": ["@octokit/app", "@octokit/core", "@octokit/oauth-app", "@octokit/plugin-paginate-graphql", "@octokit/plugin-paginate-rest", "@octokit/plugin-rest-endpoint-methods", "@octokit/plugin-retry", "@octokit/plugin-throttling", "@octokit/request-error", "@octokit/types"]}, "toad-cache@3.7.0": {"integrity": "sha512-/m8M+2BJUpoJdgAHoG+baCwBT+tf2VraSfkBgl0Y00qIWt41DJ8R5B8nsEw0I58YwF5IZH6z24/2TobDKnqSWw=="}, "universal-github-app-jwt@2.2.0": {"integrity": "sha512-G5o6f95b5BggDGuUfKDApKaCgNYy2x7OdHY0zSMF081O0EJobw+1130VONhrA7ezGSV2FNOGyM+KQpQZAr9bIQ=="}, "universal-user-agent@7.0.2": {"integrity": "sha512-0JCqzSKnStlRRQfCdowvqy3cy0Dvtlb8xecj/H8JFZuCze4rwjPZQOgvFvn0Ws/usCHQFGpyr+pB9adaGwXn4Q=="}}}