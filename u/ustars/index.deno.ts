import { starsList, createStarList, addRepoToStarList } from "./github.deno.ts";
import { MongoClient, ServerApiVersion } from "npm:mongodb";
import { createDeepSeek } from "npm:@ai-sdk/deepseek";
import { generateObject } from "npm:ai";
import { z } from "npm:zod";
import PQueue from "npm:p-queue";

const deepseek = createDeepSeek({
  apiKey: "***********************************",
});

export async function generateDescription(starList: any, repo: any) {
  const { object } = await generateObject({
    model: deepseek("deepseek-coder"),
    schema: z.object({
      canAddToStarList: z.boolean().describe("是否符合用户的收藏需求"),
    }),
    messages: [
      {
        role: "system",
        content: `
           你是一个智能推荐助手，擅长分析 GitHub 仓库并进行精准匹配。

请根据以下信息:

【用户收藏列表】
${starList.name}: ${starList.description}

【待分析仓库】
${repo.name}: ${repo.description}
topics: ${repo.topics.join(",")}

分析任务：
1. 仔细阅读该仓库的 description 和 topic 内容
2. 分析仓库的主要功能、技术特点和应用场景
3. 将仓库与用户的 star lists 进行匹配
4. 判断该仓库是否符合用户的收藏需求`,
      },
    ],
  });

  return object;
}

const uri =
  "******************************************************************************";

const client = new MongoClient(uri, {
  serverApi: {
    version: ServerApiVersion.v1,
    strict: false,
    deprecationErrors: true,
  },
});

export async function main() {
  const db = client.db("1kgithub");
  const collection = db.collection("repos");
  const repos = await collection
    .find({})
    .sort({ stars: -1 })
    .limit(10000)
    .toArray();

  const starLists = await starsList();

  // 创建队列，限制并发数为10
  const queue = new PQueue({
    concurrency: 100,
    interval: 2000,
    intervalCap: 50,
  });
  let processedCount = 0;
  // 立即添加事件监听
  queue.on("active", () => {
    console.log(
      `正在处理，当前已完成: ${processedCount}，队列中还有: ${queue.size}`
    );
  });

  const starList = starLists[0];
  // 处理从索引100到10000的仓库
  console.time("generateDescription");
  repos.slice(3000, 10000).forEach((repo) => {
    // 为每个仓库添加一个任务到队列
    queue.add(async () => {
      const res = await generateDescription(starList, repo);
      processedCount++;
      if (res.canAddToStarList) {
        try {
          console.time(`addRepoToStarList-${repo.repo_id}`);
          await addRepoToStarList(starList.id, repo.repo_id);
        } catch (error) {
          console.error("添加仓库到 Star List 失败:", error);
        }
      } else {
        console.log("🚀 ~ main ~ repo:", repo.repo_id);
      }
      return res;
    });
  });

  // 等待所有任务完成
  await queue.onIdle();

  console.log(`总共处理了 ${processedCount} 个仓库`);
}

main();
