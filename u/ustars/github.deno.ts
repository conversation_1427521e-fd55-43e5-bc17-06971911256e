import { Octokit } from "npm:octokit";

// 定义GraphQL响应类型
interface Repository {
  id: string;
  nameWithOwner: string;
  description: string | null;
}

interface UserList {
  id: string;
  name: string;
  description: string | null;
  items: {
    totalCount: number;
    nodes: Repository[];
  };
}

interface GraphQLResponse<T> {
  [key: string]: any;
}

const octokit = new Octokit({
  auth: "****************************************",
});

export async function starsList() {
  const existingListsQuery = `
      query {
        viewer {
          lists(first: 10) {
            nodes {
              id
              name
              description
              items(first: 5) {
                totalCount
                nodes {
                  ... on Repository {
                    id
                    nameWithOwner
                    description
                  }
                }
              }
            }
          }
        }
      }
    `;

  const existingListsResult = await octokit.graphql<
    GraphQLResponse<{
      viewer: { lists: { nodes: UserList[] } };
    }>
  >(existingListsQuery);
  const starLists = existingListsResult.viewer.lists.nodes;
  return starLists;
}

export async function createStarList(name: string, description: string) {
  const newListName = name;
  const newListDescription = description;

  const createListMutation = `
      mutation CreateUserList($name: String!, $description: String) {
        createUserList(input: {
          name: $name,
          description: $description
        }) {
          list {
            id
            name
            description
          }
        }
      }
    `;

  const createListResult = await octokit.graphql<
    GraphQLResponse<{
      createUserList: {
        list: { id: string; name: string; description: string | null };
      };
    }>
  >(createListMutation, {
    name: newListName,
    description: newListDescription,
  });

  const newListId = createListResult.createUserList.list.id;
  console.log(`成功创建新的Star List: "${newListName}" (ID: ${newListId})`);
  return newListId;
}

export async function addRepoToStarList(listId: string, repoId: string) {
  const addToListMutation = `
    mutation UpdateUserListsForItem($listIds: [ID!]!, $itemId: ID!) {
      updateUserListsForItem(input: {
        listIds: $listIds,
        itemId: $itemId
      }) {
        lists {
          id
          name
        }
      }
    }
  `;

  const addToListResult = await octokit.graphql(addToListMutation, {
    listIds: [listId],
    itemId: repoId,
  });

  return addToListResult;
}


export async function main() {
  const starLists = await starsList();
  console.log(starLists);
}