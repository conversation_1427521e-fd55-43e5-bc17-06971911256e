# 基于数据库的套利服务 (ArbitrageDbService)

## 概述

`ArbitrageDbService` 是一个基于数据库的套利服务，它在原有的 `ArbitrageService` 基础上增加了任务管理、流程记录、状态跟踪和恢复功能。通过数据库持久化，可以实现套利任务的可靠执行和故障恢复。

## 主要特性

- ✅ **任务持久化**: 所有套利任务和步骤都记录在数据库中
- ✅ **故障恢复**: 支持从任意步骤恢复中断的任务
- ✅ **状态监控**: 实时跟踪任务执行进度和状态
- ✅ **重试机制**: 自动重试失败的任务（可配置重试次数）
- ✅ **任务管理**: 创建、取消、监控套利任务
- ✅ **报告生成**: 详细的任务执行报告和盈利分析

## 架构设计

```
ArbitrageDbService
├── ArbitrageService (原有套利逻辑)
├── TaskManager (任务管理)
│   ├── TaskAdapter (任务数据访问)
│   └── TaskDetailAdapter (任务详情数据访问)
└── PostgreSQL Database
    ├── task (任务主表)
    └── task_detail (任务步骤详情表)
```

## 快速开始

### 1. 环境配置

确保已配置以下环境变量：

```bash
# 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/arbitrage_db

# 交易所配置
BINANCE_API_KEY=your_binance_api_key
BINANCE_API_SECRET=your_binance_api_secret

# 区块链配置
IOTEX_PRIVATE_KEY=your_iotex_private_key
IOTEX_ADDRESS=your_iotex_address
SOLANA_PRIVATE_KEY=your_solana_private_key
SOLANA_ADDRESS=your_solana_address

# RPC 节点
IOTEX_RPC_URL=https://babel-api.mainnet.iotex.io
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
```

### 2. 数据库初始化

运行 SQL 脚本创建必要的表：

```bash
psql -d arbitrage_db -f u/sql/arbitrage_tables.sql
```

### 3. 基本使用

```typescript
import { ArbitrageDbService } from "./libs/arb-db";
import { ArbitrageStrategy } from "./tools/task";

// 初始化服务
const arbDbService = new ArbitrageDbService(
  cexService,
  mimoService,
  bridgeService,
  solanaService,
  {
    maxTradeAmount: 1000,
    walletAddresses: {
      iotex: "your_iotex_address",
      solana: "your_solana_address"
    }
  }
);

// 执行套利任务
const result = await arbDbService.executeArbitrageTask(
  undefined, // 任务ID (新任务为 undefined)
  ArbitrageStrategy.CEX_TO_DEX, // 策略类型
  100 // 交易金额 (USDT)
);

console.log("套利结果:", result);
```

## 主要方法

### 任务执行

```typescript
// 创建新任务
const taskId = await arbDbService.createArbitrageTask(
  ArbitrageStrategy.CEX_TO_DEX,
  100,
  { cexSolPrice: 150, cexIotxPrice: 0.05 } // 可选价格信息
);

// 执行任务（新任务或恢复任务）
const result = await arbDbService.executeArbitrageTask(taskId);

// 自动检测并恢复未完成任务
const result = await arbDbService.executeArbitrageTask(
  undefined,
  ArbitrageStrategy.CEX_TO_DEX
);
```

### 任务监控

```typescript
// 获取任务状态
const status = await arbDbService.getTaskStatus(taskId);
console.log(`进度: ${status.progress}%`);
console.log(`当前步骤: ${status.task.current_step}`);

// 获取详细报告
const report = await arbDbService.getTaskReport(taskId);
console.log(`执行时间: ${report.executionTime}`);
console.log(`步骤统计:`, report.stepStats);

// 获取所有运行中的任务
const runningTasks = await arbDbService.getRunningTasks();
```

### 错误处理

```typescript
// 重试失败的任务
try {
  const result = await arbDbService.retryFailedTask(taskId);
} catch (error) {
  console.error("重试失败:", error);
  
  // 取消任务
  await arbDbService.cancelTask(taskId);
}
```

## 任务状态说明

### 任务状态 (TaskStatus)
- `running`: 任务正在执行
- `completed`: 任务已完成
- `failed`: 任务执行失败
- `timeout`: 任务执行超时

### 步骤状态 (StepStatus)
- `pending`: 步骤等待执行
- `running`: 步骤正在执行
- `completed`: 步骤已完成
- `failed`: 步骤执行失败

### 套利步骤 (ArbitrageStep)

**CEX -> DEX 策略步骤:**
1. `buy_token`: 在 CEX 购买代币
2. `withdraw_token`: 提现代币到链上
3. `swap_token`: 在 DEX 交换代币
4. `bridge_transfer`: 跨链转移
5. `unwrap_sol`: 解包 wSOL
6. `transfer_to_cex`: 转账到 CEX
7. `sell_token`: 在 CEX 卖出代币

**DEX -> CEX 策略步骤:**
1. `buy_token`: 在 CEX 购买代币
2. `withdraw_token`: 提现代币到链上
3. `bridge_transfer`: 跨链转移
4. `swap_token`: 在 DEX 交换代币
5. `transfer_to_cex`: 转账到 CEX
6. `sell_token`: 在 CEX 卖出代币

## 故障恢复机制

系统支持从任意步骤恢复中断的任务：

1. **自动检测**: 启动时自动检测未完成的任务
2. **状态恢复**: 从数据库中恢复任务的执行状态
3. **步骤续执**: 从中断的步骤继续执行
4. **数据一致性**: 确保恢复后的数据状态一致

## 监控和维护

### 任务监控
```typescript
// 实时监控任务进度
async function monitorTask(taskId: string) {
  const status = await arbDbService.getTaskStatus(taskId);
  console.log(`进度: ${status.progress}%`);
  
  // 检查是否有失败的步骤
  const failedSteps = status.steps.filter(s => s.status === 'failed');
  if (failedSteps.length > 0) {
    console.log("失败的步骤:", failedSteps);
  }
}
```

### 数据清理
```typescript
// 清理30天前的已完成任务
const cleanedCount = await arbDbService.cleanupOldTasks(30);
console.log(`已清理 ${cleanedCount} 个旧任务`);
```

## 最佳实践

1. **定期监控**: 定期检查任务状态，及时处理失败的任务
2. **合理重试**: 设置合适的重试次数，避免无限重试
3. **数据备份**: 定期备份任务数据，防止数据丢失
4. **日志记录**: 启用详细日志，便于问题排查
5. **资源管理**: 合理控制并发任务数量，避免资源竞争

## 注意事项

- 确保数据库连接稳定，避免任务状态不一致
- 在生产环境中使用时，建议设置适当的超时时间
- 定期清理已完成的旧任务，避免数据库膨胀
- 监控系统资源使用情况，确保服务稳定运行

## 示例代码

完整的使用示例请参考 `u/example-arb-db-usage.ts` 文件。
