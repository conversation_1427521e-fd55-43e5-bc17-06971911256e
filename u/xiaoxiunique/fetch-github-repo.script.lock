{"version": "4", "specifiers": {"npm:@clickhouse/client@*": "1.10.1", "npm:octokit@*": "4.1.2_@octokit+core@6.1.4", "npm:url@*": "0.11.4"}, "npm": {"@clickhouse/client-common@1.10.1": {"integrity": "sha512-Duh3cign2ChvXABpjVj9Hkz5y20Zf48OE0Y50S4qBVPdhI81S4Rh4MI/bEwvwMnzHubSkiEQ+VhC5HzV8ybnpg=="}, "@clickhouse/client@1.10.1": {"integrity": "sha512-Ot/6l4hFALK6NtZDS2UegukfRXWkkftWHCnzKUwanpOQ3Jd+RVKx5dxQreeBG5XcRjt1xyf5904PFjbCnaulXg==", "dependencies": ["@clickhouse/client-common"]}, "@octokit/app@15.1.5_@octokit+core@6.1.4": {"integrity": "sha512-6cxLT9U8x7GGQ7lNWsKtFr4ccg9oLkGvowk373sX9HvX5U37kql5d55SzaQUxPE8PwgX2cqkzDm5NF5aPKevqg==", "dependencies": ["@octokit/auth-app", "@octokit/auth-unauthenticated", "@octokit/core", "@octokit/oauth-app", "@octokit/plugin-paginate-rest", "@octokit/types", "@octokit/webhooks"]}, "@octokit/auth-app@7.1.5": {"integrity": "sha512-boklS4E6LpbA3nRx+SU2fRKRGZJdOGoSZne/i3Y0B5rfHOcGwFgcXrwDLdtbv4igfDSnAkZaoNBv1GYjPDKRNw==", "dependencies": ["@octokit/auth-oauth-app", "@octokit/auth-oauth-user", "@octokit/request", "@octokit/request-error", "@octokit/types", "toad-cache", "universal-github-app-jwt", "universal-user-agent"]}, "@octokit/auth-oauth-app@8.1.3": {"integrity": "sha512-4e6OjVe5rZ8yBe8w7byBjpKtSXFuro7gqeGAAZc7QYltOF8wB93rJl2FE0a4U1Mt88xxPv/mS+25/0DuLk0Ewg==", "dependencies": ["@octokit/auth-oauth-device", "@octokit/auth-oauth-user", "@octokit/request", "@octokit/types", "universal-user-agent"]}, "@octokit/auth-oauth-device@7.1.3": {"integrity": "sha512-BECO/N4B/Uikj0w3GCvjf/odMujtYTP3q82BJSjxC2J3rxTEiZIJ+z2xnRlDb0IE9dQSaTgRqUPVOieSbFcVzg==", "dependencies": ["@octokit/oauth-methods", "@octokit/request", "@octokit/types", "universal-user-agent"]}, "@octokit/auth-oauth-user@5.1.3": {"integrity": "sha512-zNPByPn9K7TC+OOHKGxU+MxrE9SZAN11UHYEFLsK2NRn3akJN2LHRl85q+Eypr3tuB2GrKx3rfj2phJdkYCvzw==", "dependencies": ["@octokit/auth-oauth-device", "@octokit/oauth-methods", "@octokit/request", "@octokit/types", "universal-user-agent"]}, "@octokit/auth-token@5.1.2": {"integrity": "sha512-JcQDsBdg49Yky2w2ld20IHAlwr8d/d8N6NiOXbtuoPCqzbsiJgF633mVUw3x4mo0H5ypataQIX7SFu3yy44Mpw=="}, "@octokit/auth-unauthenticated@6.1.2": {"integrity": "sha512-07DlUGcz/AAVdzu3EYfi/dOyMSHp9YsOxPl/MPmtlVXWiD//GlV8HgZsPhud94DEyx+RfrW0wSl46Lx+AWbOlg==", "dependencies": ["@octokit/request-error", "@octokit/types"]}, "@octokit/core@6.1.4": {"integrity": "sha512-lAS9k7d6I0MPN+gb9bKDt7X8SdxknYqAMh44S5L+lNqIN2NuV8nvv3g8rPp7MuRxcOpxpUIATWprO0C34a8Qmg==", "dependencies": ["@octokit/auth-token", "@octokit/graphql", "@octokit/request", "@octokit/request-error", "@octokit/types", "before-after-hook", "universal-user-agent"]}, "@octokit/endpoint@10.1.3": {"integrity": "sha512-nBRBMpKPhQUxCsQQeW+rCJ/OPSMcj3g0nfHn01zGYZXuNDvvXudF/TYY6APj5THlurerpFN4a/dQAIAaM6BYhA==", "dependencies": ["@octokit/types", "universal-user-agent"]}, "@octokit/graphql@8.2.1": {"integrity": "sha512-n57hXtOoHrhwTWdvhVkdJHdhTv0JstjDbDRhJfwIRNfFqmSo1DaK/mD2syoNUoLCyqSjBpGAKOG0BuwF392slw==", "dependencies": ["@octokit/request", "@octokit/types", "universal-user-agent"]}, "@octokit/oauth-app@7.1.6": {"integrity": "sha512-OMcMzY2WFARg80oJNFwWbY51TBUfLH4JGTy119cqiDawSFXSIBujxmpXiKbGWQlvfn0CxE6f7/+c6+Kr5hI2YA==", "dependencies": ["@octokit/auth-oauth-app", "@octokit/auth-oauth-user", "@octokit/auth-unauthenticated", "@octokit/core", "@octokit/oauth-authorization-url", "@octokit/oauth-methods", "@types/aws-lambda", "universal-user-agent"]}, "@octokit/oauth-authorization-url@7.1.1": {"integrity": "sha512-ooXV8GBSabSWyhLUowlMIVd9l1s2nsOGQdlP2SQ4LnkEsGXzeCvbSbCPdZThXhEFzleGPwbapT0Sb+YhXRyjCA=="}, "@octokit/oauth-methods@5.1.4": {"integrity": "sha512-Jc/ycnePClOvO1WL7tlC+TRxOFtyJBGuTDsL4dzXNiVZvzZdrPuNw7zHI3qJSUX2n6RLXE5L0SkFmYyNaVUFoQ==", "dependencies": ["@octokit/oauth-authorization-url", "@octokit/request", "@octokit/request-error", "@octokit/types"]}, "@octokit/openapi-types@23.0.1": {"integrity": "sha512-izFjMJ1sir0jn0ldEKhZ7xegCTj/ObmEDlEfpFrx4k/JyZSMRHbO3/rBwgE7f3m2DHt+RrNGIVw4wSmwnm3t/g=="}, "@octokit/openapi-webhooks-types@10.1.1": {"integrity": "sha512-qBfqQVIDQaCFeGCofXieJDwvXcGgDn17+UwZ6WW6lfEvGYGreLFzTiaz9xjet9Us4zDf8iasoW3ixUj/R5lMhA=="}, "@octokit/plugin-paginate-graphql@5.2.4_@octokit+core@6.1.4": {"integrity": "sha512-pLZES1jWaOynXKHOqdnwZ5ULeVR6tVVCMm+AUbp0htdcyXDU95WbkYdU4R2ej1wKj5Tu94Mee2Ne0PjPO9cCyA==", "dependencies": ["@octokit/core"]}, "@octokit/plugin-paginate-rest@11.4.3_@octokit+core@6.1.4": {"integrity": "sha512-tBXaAbXkqVJlRoA/zQVe9mUdb8rScmivqtpv3ovsC5xhje/a+NOCivs7eUhWBwCApJVsR4G5HMeaLbq7PxqZGA==", "dependencies": ["@octokit/core", "@octokit/types"]}, "@octokit/plugin-rest-endpoint-methods@13.3.1_@octokit+core@6.1.4": {"integrity": "sha512-o8uOBdsyR+WR8MK9Cco8dCgvG13H1RlM1nWnK/W7TEACQBFux/vPREgKucxUfuDQ5yi1T3hGf4C5ZmZXAERgwQ==", "dependencies": ["@octokit/core", "@octokit/types"]}, "@octokit/plugin-retry@7.1.4_@octokit+core@6.1.4": {"integrity": "sha512-7AIP4p9TttKN7ctygG4BtR7rrB0anZqoU9ThXFk8nETqIfvgPUANTSYHqWYknK7W3isw59LpZeLI8pcEwiJdRg==", "dependencies": ["@octokit/core", "@octokit/request-error", "@octokit/types", "bottleneck"]}, "@octokit/plugin-throttling@9.4.0_@octokit+core@6.1.4": {"integrity": "sha512-IOlXxXhZA4Z3m0EEYtrrACkuHiArHLZ3CvqWwOez/pURNqRuwfoFlTPbN5Muf28pzFuztxPyiUiNwz8KctdZaQ==", "dependencies": ["@octokit/core", "@octokit/types", "bottleneck"]}, "@octokit/request-error@6.1.7": {"integrity": "sha512-69NIppAwaauwZv6aOzb+VVLwt+0havz9GT5YplkeJv7fG7a40qpLt/yZKyiDxAhgz0EtgNdNcb96Z0u+Zyuy2g==", "dependencies": ["@octokit/types"]}, "@octokit/request@9.2.2": {"integrity": "sha512-dZl0ZHx6gOQGcffgm1/Sf6JfEpmh34v3Af2Uci02vzUYz6qEN6zepoRtmybWXIGXFIK8K9ylE3b+duCWqhArtg==", "dependencies": ["@octokit/endpoint", "@octokit/request-error", "@octokit/types", "fast-content-type-parse", "universal-user-agent"]}, "@octokit/types@13.8.0": {"integrity": "sha512-x7DjTIbEpEWXK99DMd01QfWy0hd5h4EN+Q7shkdKds3otGQP+oWE/y0A76i1OvH9fygo4ddvNf7ZvF0t78P98A==", "dependencies": ["@octokit/openapi-types"]}, "@octokit/webhooks-methods@5.1.1": {"integrity": "sha512-NGlEHZDseJTCj8TMMFehzwa9g7On4KJMPVHDSrHxCQumL6uSQR8wIkP/qesv52fXqV1BPf4pTxwtS31ldAt9Xg=="}, "@octokit/webhooks@13.7.4": {"integrity": "sha512-f386XyLTieQbgKPKS6ZMlH4dq8eLsxNddwofiKRZCq0bZ2gikoFwMD99K6l1oAwqe/KZNzrEziGicRgnzplplQ==", "dependencies": ["@octokit/openapi-webhooks-types", "@octokit/request-error", "@octokit/webhooks-methods"]}, "@types/aws-lambda@8.10.147": {"integrity": "sha512-nD0Z9fNIZcxYX5Mai2CTmFD7wX7UldCkW2ezCF8D1T5hdiLsnTWDGRpfRYntU6VjTdLQjOvyszru7I1c1oCQew=="}, "before-after-hook@3.0.2": {"integrity": "sha512-Nik3Sc0ncrMK4UUdXQmAnRtzmNQTAAXmXIopizwZ1W1t8QmfJj+zL4OA2I7XPTPW5z5TDqv4hRo/JzouDJnX3A=="}, "bottleneck@2.19.5": {"integrity": "sha512-VHiNCbI1lKdl44tGrhNfU3lup0Tj/ZBMJB5/2ZbNXRCPuRCO7ed2mgcK4r17y+KB2EfuYuRaVlwNbAeaWGSpbw=="}, "call-bind-apply-helpers@1.0.2": {"integrity": "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==", "dependencies": ["es-errors", "function-bind"]}, "call-bound@1.0.4": {"integrity": "sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==", "dependencies": ["call-bind-apply-helpers", "get-intrinsic"]}, "dunder-proto@1.0.1": {"integrity": "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==", "dependencies": ["call-bind-apply-helpers", "es-errors", "gopd"]}, "es-define-property@1.0.1": {"integrity": "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g=="}, "es-errors@1.3.0": {"integrity": "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw=="}, "es-object-atoms@1.1.1": {"integrity": "sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==", "dependencies": ["es-errors"]}, "fast-content-type-parse@2.0.1": {"integrity": "sha512-nGqtvLrj5w0naR6tDPfB4cUmYCqouzyQiz6C5y/LtcDllJdrcc6WaWW6iXyIIOErTa/XRybj28aasdn4LkVk6Q=="}, "function-bind@1.1.2": {"integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA=="}, "get-intrinsic@1.3.0": {"integrity": "sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==", "dependencies": ["call-bind-apply-helpers", "es-define-property", "es-errors", "es-object-atoms", "function-bind", "get-proto", "gopd", "has-symbols", "hasown", "math-intrinsics"]}, "get-proto@1.0.1": {"integrity": "sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==", "dependencies": ["dunder-proto", "es-object-atoms"]}, "gopd@1.2.0": {"integrity": "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg=="}, "has-symbols@1.1.0": {"integrity": "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ=="}, "hasown@2.0.2": {"integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==", "dependencies": ["function-bind"]}, "math-intrinsics@1.1.0": {"integrity": "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g=="}, "object-inspect@1.13.4": {"integrity": "sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew=="}, "octokit@4.1.2_@octokit+core@6.1.4": {"integrity": "sha512-0kcTxJOK3yQrJsRb8wKa28hlTze4QOz4sLuUnfXXnhboDhFKgv8LxS86tFwbsafDW9JZ08ByuVAE8kQbYJIZkA==", "dependencies": ["@octokit/app", "@octokit/core", "@octokit/oauth-app", "@octokit/plugin-paginate-graphql", "@octokit/plugin-paginate-rest", "@octokit/plugin-rest-endpoint-methods", "@octokit/plugin-retry", "@octokit/plugin-throttling", "@octokit/request-error", "@octokit/types"]}, "punycode@1.4.1": {"integrity": "sha512-jmYNElW7yvO7TV33CjSmvSiE2yco3bV2czu/OzDKdMNVZQWfxCblURLhf+47syQRBntjfLdd/H0egrzIG+oaFQ=="}, "qs@6.14.0": {"integrity": "sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w==", "dependencies": ["side-channel"]}, "side-channel-list@1.0.0": {"integrity": "sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==", "dependencies": ["es-errors", "object-inspect"]}, "side-channel-map@1.0.1": {"integrity": "sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==", "dependencies": ["call-bound", "es-errors", "get-intrinsic", "object-inspect"]}, "side-channel-weakmap@1.0.2": {"integrity": "sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==", "dependencies": ["call-bound", "es-errors", "get-intrinsic", "object-inspect", "side-channel-map"]}, "side-channel@1.1.0": {"integrity": "sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==", "dependencies": ["es-errors", "object-inspect", "side-channel-list", "side-channel-map", "side-channel-weakmap"]}, "toad-cache@3.7.0": {"integrity": "sha512-/m8M+2BJUpoJdgAHoG+baCwBT+tf2VraSfkBgl0Y00qIWt41DJ8R5B8nsEw0I58YwF5IZH6z24/2TobDKnqSWw=="}, "universal-github-app-jwt@2.2.0": {"integrity": "sha512-G5o6f95b5BggDGuUfKDApKaCgNYy2x7OdHY0zSMF081O0EJobw+1130VONhrA7ezGSV2FNOGyM+KQpQZAr9bIQ=="}, "universal-user-agent@7.0.2": {"integrity": "sha512-0JCqzSKnStlRRQfCdowvqy3cy0Dvtlb8xecj/H8JFZuCze4rwjPZQOgvFvn0Ws/usCHQFGpyr+pB9adaGwXn4Q=="}, "url@0.11.4": {"integrity": "sha512-oCwdVC7mTuWiPyjLUz/COz5TLk6wgp0RCsN+wHZ2Ekneac9w8uuV0njcbbie2ME+Vs+d6duwmYuR3HgQXs1fOg==", "dependencies": ["punycode", "qs"]}}}