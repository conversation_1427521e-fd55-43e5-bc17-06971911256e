import { MongoClient, ServerApiVersion } from "npm:mongodb";
import { createDeepSeek } from "npm:@ai-sdk/deepseek";
import { generateObject } from "npm:ai";
import { z } from "npm:zod";

const deepseek = createDeepSeek({
  apiKey: "***********************************",
});

export async function generateDescription(repo: any) {
  const { object } = await generateObject({
    model: deepseek("deepseek-coder"),
    schema: z.object({
      cn_description: z.string().describe("中文描述"),
      en_description: z.string().describe("英文描述"),
    }),
    prompt: `
    请根据我提供的 Github Description 信息，生成一个中文描述和英文描述。
    Github Name: ${repo.name}
    Github Description: ${repo.description}
    Github URL: ${repo.html_url}
    `,
  });

  return object;
}

export async function main() {
  const uri =
    "mongodb+srv://1kgithub:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0";

  const client = new MongoClient(uri, {
    serverApi: {
      version: ServerApiVersion.v1,
      strict: false,
      deprecationErrors: true,
    },
  });

  const db = client.db("1kgithub");
  const repos = db.collection("repos");

  const list = await repos
    .find({
      $or: [
        {
          cn_description: {
            $exists: false,
          },
        },
        {
          en_description: {
            $exists: false,
          },
        },
      ],
    })
    .limit(100)
    .toArray();

  if (list.length === 0) {
    console.log("没有需要更新的仓库");
    return;
  }

  const promises = list.map(async (repo) => {
    const { cn_description, en_description } = await generateDescription(repo);
    await repos.updateOne(
      {
        _id: repo._id,
      },
      { $set: { cn_description, en_description } }
    );
    console.log(`更新 ${repo.name} 的描述`);
  });

  await Promise.all(promises);
}
