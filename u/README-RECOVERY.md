# 套利流程恢复系统

## 🎯 解决的问题

原有的套利逻辑存在一个关键问题：**如果中途某一步失败了，就没办法恢复执行**。

现在每次执行套利时会自动检查是否有未完成的流程，如果有就先恢复执行，然后再开始新的套利。

## 🚀 核心特性

- ✅ **自动检查**: 执行套利时自动检查未完成流程
- ✅ **断点续传**: 从失败步骤自动恢复执行
- ✅ **自动重试**: 失败步骤自动重试，最多3次
- ✅ **超时处理**: 超过2小时的流程自动标记为失败

## 📋 流程步骤

### CEX -> DEX 策略
1. `BUY_TOKEN` - 在CEX购买IOTX
2. `WITHDRAW_TOKEN` - 提现IOTX到IoTeX钱包
3. `SWAP_TOKEN` - 在MIMO将IOTX换成SOL
4. `BRIDGE_TRANSFER` - 跨链转移SOL到Solana
5. `UNWRAP_SOL` - 解包wSOL
6. `TRANSFER_TO_CEX` - 转账到CEX
7. `SELL_TOKEN` - 卖出SOL
8. `COMPLETED` - 完成

### DEX -> CEX 策略
1. `BUY_TOKEN` - 在CEX购买SOL
2. `WITHDRAW_TOKEN` - 提现SOL到Solana钱包
3. `BRIDGE_TRANSFER` - 跨链转移SOL到IoTeX
4. `SWAP_TOKEN` - 在MIMO将SOL换成IOTX
5. `TRANSFER_TO_CEX` - 转账IOTX到CEX
6. `SELL_TOKEN` - 卖出IOTX
7. `COMPLETED` - 完成

## 🛠️ 使用方法

**完全自动化，智能恢复！**

```typescript
import { ArbitrageService } from "./libs/arbitrage";

const arbitrageService = new ArbitrageService(/* 配置 */);

// 直接执行套利，会自动检查和恢复未完成的流程
try {
  const result = await arbitrageService.execIotexPath(100);

  if (result.recoveredFrom) {
    console.log(`恢复了之前未完成的流程 (从步骤 ${result.recoveredFrom} 开始)`);
    console.log("恢复完成，金额:", result.initialAmount);
  } else {
    console.log("新套利完成:", result.profitAnalysis);
  }
} catch (error) {
  console.log("流程已保存，下次执行时会自动恢复");
}
```

## 🧠 智能逻辑

系统会自动：
1. **检查未完成流程**: 只检查相同策略类型的流程
2. **优先恢复执行**: 如果有未完成的流程，先完成它
3. **避免重复执行**: 恢复完成后不会再开始新流程
4. **策略隔离**: CEX->DEX 和 DEX->CEX 流程互不干扰

## 🔧 配置说明

### Redis配置
系统使用现有的Redis连接配置（`u/libs/kv.ts`）：

```typescript
import { redisClient } from "./kv";
```

### 流程配置
每个流程支持以下配置：

```typescript
interface ArbitrageState {
  id: string;           // 唯一流程ID
  strategy: string;     // 策略类型
  currentStep: string;  // 当前步骤
  usdtAmount: number;   // 交易金额
  retryCount: number;   // 重试次数
  maxRetries: number;   // 最大重试次数（默认3次）
}
```

## 📊 Redis数据结构

系统使用Redis存储流程状态：

- **流程状态**: `arbitrage:{processId}` (JSON格式，24小时自动过期)
- **活跃列表**: `arbitrage:active_processes` (Set类型，存储活跃流程ID)

## 🔧 工作原理

1. **执行套利时**: 自动检查Redis中是否有相同策略的未完成流程
2. **发现未完成流程**: 从中断的步骤开始，**一直执行到完成**，然后直接返回
3. **没有未完成流程**: 开始新的套利流程
4. **完整恢复**: 恢复时会从中断点执行所有剩余步骤，直到流程完成
5. **状态管理**: 每个步骤完成后自动保存到Redis，包含：
   - 流程ID和策略类型
   - 当前执行步骤
   - 交易数据（买单、交换结果等）
   - 重试次数和错误信息
6. **自动清理**: 完成或失败的流程会自动从活跃列表中移除

## 🚨 故障处理

- **网络失败**: 自动重试最多3次
- **超时流程**: 超过2小时自动标记为失败
- **重复执行**: 所有操作都是幂等的，不会重复扣费

完全自动化，无需人工干预！
