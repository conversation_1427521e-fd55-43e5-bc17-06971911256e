import { ArbitrageDbService } from "./libs/arb-db";
import { TaskManager } from "./libs/task-manager";
import { ArbitrageStrategy, ArbitrageStep, TaskStatus } from "./tools/task";

/**
 * 测试步骤级恢复功能
 * 这个测试模拟了任务在不同步骤中断后的恢复场景
 */

// 模拟服务类
class MockCexService {
  async getSolPrice() { return 150; }
  async getIotxPrice() { return 0.05; }
  async buyIotx(amount: number) { 
    return { filled: amount / 0.05, amount: amount / 0.05, cost: amount };
  }
  async buySol(amount: number) { 
    return { filled: amount / 150, amount: amount / 150, cost: amount };
  }
  async withdrawIotx(amount: number, address: string) { return true; }
  async withdrawSol(amount: number, address: string) { return true; }
  async sellSymbol(symbol: string, amount: number) { 
    return { filled: amount, cost: amount * 150 };
  }
  async sellIotx(amount: number) { 
    return { filled: amount, cost: amount * 0.05 };
  }
  async getSolDepositAddress() { return "mock_sol_address"; }
  async getIotxDepositAddress() { return "mock_iotx_address"; }
  async waitForSolBalanceIncrease(amount: number) { return true; }
  async waitForIotxBalanceIncrease(amount: number) { return true; }
}

class MockMimoService {
  async getTokenBalanceFormatted(token: string) { return "100"; }
  async swapIotxToSol(amount: number) { 
    return { solAmount: amount * 0.001, priceImpact: "0.5" };
  }
  async swapSolToIotx(amount: number) { 
    return { iotxAmount: amount * 1000, priceImpact: "0.5" };
  }
  async sendIotx(amount: number, address: string) { return "mock_tx_hash"; }
  getWalletAddress() { return "mock_wallet"; }
}

class MockBridgeService {
  config = {
    solanaRpcUrl: "mock_rpc",
    solanaPrivateKey: "mock_key"
  };
  
  async transferSolFromIotexToSolana(amount: number, address: string) {
    return { receivedAmount: amount * 0.99, bridgeFee: amount * 0.01 };
  }
  async transferSolFromSolanaToIotex(amount: number, address: string) {
    return { receivedAmount: amount * 0.99, bridgeFee: amount * 0.01 };
  }
}

class MockSolanaService {
  constructor(config: any) {}
  async getSolBalance() { return 10; }
  async getTotalSolBalance() { return { native: 5, wsol: 5, total: 10 }; }
  async unwrapSol() { return true; }
  async transfer(address: string, amount: number) { return "mock_tx_hash"; }
}

class MockArbitrageService {
  constructor(
    private cexService: any,
    private mimoService: any,
    private bridgeService: any,
    private solanaService: any,
    private config: any
  ) {}

  // 模拟私有方法
  private async waitForIotxBalanceIncrease(amount: number) {
    console.log(`⏳ 模拟等待 IOTX 余额增加 ${amount}`);
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  private async waitForSolanaBalanceIncrease(amount: number) {
    console.log(`⏳ 模拟等待 Solana 余额增加 ${amount}`);
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  private async waitForSolBalanceIncrease(amount: number) {
    console.log(`⏳ 模拟等待 SOL 余额增加 ${amount}`);
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  private async waitForSolanaWsolBalanceIncrease(amount: number, timeout: number) {
    console.log(`⏳ 模拟等待 WSOL 余额增加 ${amount}`);
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  // 使用 bracket notation 来访问私有方法
  ['waitForIotxBalanceIncrease'] = this.waitForIotxBalanceIncrease.bind(this);
  ['waitForSolanaBalanceIncrease'] = this.waitForSolanaBalanceIncrease.bind(this);
  ['waitForSolBalanceIncrease'] = this.waitForSolBalanceIncrease.bind(this);
  ['waitForSolanaWsolBalanceIncrease'] = this.waitForSolanaWsolBalanceIncrease.bind(this);

  async execIotexPath(amount: number) {
    return { strategy: "cex_to_dex", status: "completed" };
  }

  async execSolPath(amount: number) {
    return { strategy: "dex_to_cex", status: "completed" };
  }
}

async function testStepRecovery() {
  console.log("🧪 开始测试步骤级恢复功能");

  // 创建模拟服务
  const mockCexService = new MockCexService() as any;
  const mockMimoService = new MockMimoService() as any;
  const mockBridgeService = new MockBridgeService() as any;
  const mockSolanaService = MockSolanaService as any;

  // 创建 ArbitrageDbService
  const arbDbService = new ArbitrageDbService(
    mockCexService,
    mockMimoService,
    mockBridgeService,
    mockSolanaService,
    {
      maxTradeAmount: 1000,
      walletAddresses: {
        iotex: "test_iotex_address",
        solana: "test_solana_address"
      }
    }
  );

  try {
    // 测试 1: 创建任务并模拟在不同步骤中断
    console.log("\n=== 测试 1: 创建 CEX -> DEX 任务 ===");
    const taskId = await arbDbService.createArbitrageTask(
      ArbitrageStrategy.CEX_TO_DEX,
      100
    );
    console.log(`✅ 任务创建成功: ${taskId}`);

    // 测试 2: 检查初始状态
    console.log("\n=== 测试 2: 检查初始任务状态 ===");
    const initialStatus = await arbDbService.getTaskStatus(taskId);
    console.log(`📊 初始状态: 步骤 ${initialStatus?.task.current_step}, 进度 ${initialStatus?.progress}%`);

    // 测试 3: 执行任务（应该能够完整执行）
    console.log("\n=== 测试 3: 执行完整任务 ===");
    const result = await arbDbService.executeArbitrageTask(taskId);
    console.log("✅ 任务执行成功:", result);

    // 测试 4: 检查最终状态
    console.log("\n=== 测试 4: 检查最终任务状态 ===");
    const finalStatus = await arbDbService.getTaskStatus(taskId);
    console.log(`📊 最终状态: 步骤 ${finalStatus?.task.current_step}, 进度 ${finalStatus?.progress}%`);

    // 测试 5: 创建另一个任务测试 DEX -> CEX
    console.log("\n=== 测试 5: 创建 DEX -> CEX 任务 ===");
    const taskId2 = await arbDbService.createArbitrageTask(
      ArbitrageStrategy.DEX_TO_CEX,
      150
    );
    console.log(`✅ 任务创建成功: ${taskId2}`);

    const result2 = await arbDbService.executeArbitrageTask(taskId2);
    console.log("✅ DEX -> CEX 任务执行成功:", result2);

    // 测试 6: 获取任务报告
    console.log("\n=== 测试 6: 获取任务报告 ===");
    const report1 = await arbDbService.getTaskReport(taskId);
    const report2 = await arbDbService.getTaskReport(taskId2);
    
    console.log(`📋 CEX -> DEX 报告: 状态 ${report1.status}, 执行时间 ${report1.executionTime}`);
    console.log(`📋 DEX -> CEX 报告: 状态 ${report2.status}, 执行时间 ${report2.executionTime}`);

    console.log("\n✅ 所有测试通过！步骤级恢复功能正常工作");

  } catch (error) {
    console.error("❌ 测试失败:", error);
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  testStepRecovery().catch(console.error);
}

export { testStepRecovery };
