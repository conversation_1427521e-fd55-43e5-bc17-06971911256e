import { Octokit } from "@octokit/rest";
import base64 from "base-64";
import axios from "axios";
import { PublicKey } from "@solana/web3.js";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import _ from "lodash";

// 启用 relativeTime 插件
dayjs.extend(relativeTime);

// https://github.com/iotubeproject/web-iotube-web3/blob/main/src/constants/json/tokens-all.json
export async function getAllToken(name: string) {
  const octokit = new Octokit({
    auth: "****************************************",
  });
  const response = await octokit.repos.getContent({
    owner: "iotubeproject",
    repo: "web-iotube-web3",
    path: `src/constants/json/${name}.json`,
  });

  const content = base64.decode(response.data.content);
  const tokensAll = JSON.parse(content);
  return tokensAll;
}

export async function main() {
  function decodeToHex(text: string): string {
    // Decode base64 to Uint8Array
    const decoded = atob(text);

    // Convert Uint8Array to hex string
    const hex = Array.from(decoded)
      .map((byte) => byte.charCodeAt(0).toString(16).padStart(2, "0"))
      .join("");

    return "0x" + hex;
  }

  const targetAddress = '0x8fcc471a369316df960eb6dc122d595a8c1c068c';
  const iotxToSolList = await axios.post('https://bridge.iotex.io/api/list5', {
    sender: "",
    recipient: "",
    token: "",
    first: 20,
    skip: 0,
  });
  const resList: { date: Date; msg: string }[] = [];
  const transfers = iotxToSolList.data.transfers;
  for (const item of transfers) {
    const address = decodeToHex(item.sender);
    if (address !== targetAddress) {
      continue;
    }
    const msg = `${address} OUT ${item.amount / 1e9} SOL`;
    resList.push({
        date: new Date(item.timestamp),
        msg,
    })
  }


  const solToIotxList = await axios.post('https://bridge.iotex.io/api/list1', {
    sender: "",
    recipient: "",
    token: "ofPyEdmzPyCGqACEKDbWfxObmno=",
    first: 20,
    skip: 0,
  });

  const transfers2 = solToIotxList.data.transfers;
  for(const item of transfers2) {
    const address = decodeToHex(item.recipient);
    if (address !== targetAddress) {
      continue;
    }
    const msg = `${address} IN ${item.amount / 1e9} SOL`;
    resList.push({
        date: new Date(item.timestamp),
        msg,
    })
  }

  resList.sort((a, b) => b.date.getTime() - a.date.getTime());
  for (const item of resList) {
    console.log(dayjs(item.date).format("YYYY-MM-DD HH:mm:ss"), item.msg);
  }
}
