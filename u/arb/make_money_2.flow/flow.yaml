summary: ''
description: ''
value:
  modules:
    - id: a
      value:
        type: script
        input_transforms: {}
        is_trigger: false
        path: u/arb/monitor
    - id: b
      value:
        type: script
        input_transforms:
          diff:
            type: javascript
            expr: results.a
        is_trigger: false
        path: u/arb/run1
schema:
  $schema: 'https://json-schema.org/draft/2020-12/schema'
  type: object
  order: []
  properties: {}
  required: []
