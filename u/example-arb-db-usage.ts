import { ArbitrageDbService } from "./libs/arb-db";
import { CexService } from "./libs/cex";
import { MimoService } from "./libs/mimo";
import { IoTubeBridgeService } from "./libs/iotube";
import { SolanaService } from "./libs/solana";
import { ArbitrageStrategy } from "./tools/task";

/**
 * 基于数据库的套利服务使用示例
 * 展示如何使用 ArbitrageDbService 进行套利任务的创建、执行、监控和恢复
 */

async function main() {
  // 初始化各个服务
  const cexService = new CexService({
    apiKey: process.env.BINANCE_API_KEY!,
    apiSecret: process.env.BINANCE_API_SECRET!,
    sandbox: process.env.NODE_ENV !== 'production'
  });

  const mimoService = new MimoService({
    privateKey: process.env.IOTEX_PRIVATE_KEY!,
    rpcUrl: process.env.IOTEX_RPC_URL || 'https://babel-api.mainnet.iotex.io'
  });

  const bridgeService = new IoTubeBridgeService({
    iotexRpcUrl: process.env.IOTEX_RPC_URL || 'https://babel-api.mainnet.iotex.io',
    solanaRpcUrl: process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com',
    iotexPrivateKey: process.env.IOTEX_PRIVATE_KEY!,
    solanaPrivateKey: process.env.SOLANA_PRIVATE_KEY!
  });

  const solanaService = new SolanaService({
    rpcUrl: process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com',
    privateKey: process.env.SOLANA_PRIVATE_KEY!
  });

  // 创建基于数据库的套利服务
  const arbDbService = new ArbitrageDbService(
    cexService,
    mimoService,
    bridgeService,
    solanaService,
    {
      maxTradeAmount: 1000,
      walletAddresses: {
        iotex: process.env.IOTEX_ADDRESS!,
        solana: process.env.SOLANA_ADDRESS!
      }
    }
  );

  try {
    console.log("🚀 开始基于数据库的套利服务示例");

    // 示例 1: 创建并执行新的 CEX -> DEX 套利任务
    console.log("\n=== 示例 1: 创建并执行 CEX -> DEX 套利任务 ===");
    const result1 = await arbDbService.executeArbitrageTask(
      undefined, // 没有现有任务ID
      ArbitrageStrategy.CEX_TO_DEX,
      100 // $100 USDT
    );
    console.log("CEX -> DEX 套利结果:", result1);

    // 示例 2: 检查任务状态
    console.log("\n=== 示例 2: 检查任务状态 ===");
    const taskStatus = await arbDbService.getTaskStatus(result1.taskId);
    console.log("任务状态:", taskStatus);

    // 示例 3: 获取任务报告
    console.log("\n=== 示例 3: 获取任务报告 ===");
    const taskReport = await arbDbService.getTaskReport(result1.taskId);
    console.log("任务报告:", taskReport);

    // 示例 4: 创建并执行 DEX -> CEX 套利任务
    console.log("\n=== 示例 4: 创建并执行 DEX -> CEX 套利任务 ===");
    const result2 = await arbDbService.executeArbitrageTask(
      undefined,
      ArbitrageStrategy.DEX_TO_CEX,
      150 // $150 USDT
    );
    console.log("DEX -> CEX 套利结果:", result2);

    // 示例 5: 获取所有运行中的任务
    console.log("\n=== 示例 5: 获取运行中的任务 ===");
    const runningTasks = await arbDbService.getRunningTasks();
    console.log("运行中的任务:", runningTasks);

    // 示例 6: 恢复未完成的任务（如果有的话）
    console.log("\n=== 示例 6: 恢复未完成的任务 ===");
    try {
      const resumeResult = await arbDbService.executeArbitrageTask(
        undefined,
        ArbitrageStrategy.CEX_TO_DEX
      );
      console.log("恢复任务结果:", resumeResult);
    } catch (error) {
      console.log("没有需要恢复的任务:", error.message);
    }

    // 示例 7: 演示步骤级恢复功能
    console.log("\n=== 示例 7: 步骤级恢复演示 ===");
    await demonstrateStepRecovery(arbDbService);

  } catch (error) {
    console.error("❌ 套利服务执行失败:", error);

    // 示例 7: 错误处理 - 重试失败的任务
    if (error.taskId) {
      console.log("\n=== 示例 7: 重试失败的任务 ===");
      try {
        const retryResult = await arbDbService.retryFailedTask(error.taskId);
        console.log("重试结果:", retryResult);
      } catch (retryError) {
        console.error("重试失败:", retryError);
        
        // 如果重试也失败，可以选择取消任务
        console.log("取消失败的任务...");
        await arbDbService.cancelTask(error.taskId);
      }
    }
  }

  console.log("\n✅ 基于数据库的套利服务示例完成");
}

/**
 * 监控任务进度的示例函数
 */
async function monitorTask(arbDbService: ArbitrageDbService, taskId: string) {
  console.log(`📊 开始监控任务: ${taskId}`);
  
  const checkInterval = 30000; // 30秒检查一次
  const maxMonitorTime = 1800000; // 最多监控30分钟
  
  const startTime = Date.now();
  
  while (Date.now() - startTime < maxMonitorTime) {
    try {
      const status = await arbDbService.getTaskStatus(taskId);
      if (!status) {
        console.log("❌ 任务不存在");
        break;
      }
      
      console.log(`📈 任务进度: ${status.progress.toFixed(1)}% - 状态: ${status.task.status}`);
      
      if (status.task.status === 'completed' || status.task.status === 'failed') {
        console.log("✅ 任务已完成或失败，停止监控");
        break;
      }
      
      await new Promise(resolve => setTimeout(resolve, checkInterval));
    } catch (error) {
      console.error("监控任务时出错:", error);
      await new Promise(resolve => setTimeout(resolve, checkInterval));
    }
  }
}

/**
 * 演示步骤级恢复功能
 */
async function demonstrateStepRecovery(arbDbService: ArbitrageDbService) {
  console.log("🔄 演示步骤级恢复功能...");

  try {
    // 1. 创建一个任务
    const taskId = await arbDbService.createArbitrageTask(
      ArbitrageStrategy.CEX_TO_DEX,
      50 // 较小金额用于演示
    );
    console.log(`📝 创建演示任务: ${taskId}`);

    // 2. 模拟任务执行到某个步骤后中断
    console.log("⚡ 模拟任务在执行过程中中断...");

    // 3. 检查任务当前状态
    const statusBefore = await arbDbService.getTaskStatus(taskId);
    console.log(`📊 中断前状态: 步骤 ${statusBefore?.task.current_step}, 进度 ${statusBefore?.progress}%`);

    // 4. 尝试恢复任务（从中断的步骤继续）
    console.log("🔧 从中断点恢复任务执行...");
    const recoveryResult = await arbDbService.executeArbitrageTask(taskId);
    console.log("✅ 任务恢复成功:", recoveryResult);

    // 5. 检查最终状态
    const statusAfter = await arbDbService.getTaskStatus(taskId);
    console.log(`📊 恢复后状态: 步骤 ${statusAfter?.task.current_step}, 进度 ${statusAfter?.progress}%`);

  } catch (error) {
    console.error("❌ 步骤级恢复演示失败:", error);
  }
}

/**
 * 清理旧任务的示例
 */
async function cleanupExample(arbDbService: ArbitrageDbService) {
  console.log("🧹 清理30天前的已完成任务...");
  const cleanedCount = await arbDbService.cleanupOldTasks(30);
  console.log(`✅ 已清理 ${cleanedCount} 个旧任务`);
}

// 如果直接运行此文件，执行主函数
if (require.main === module) {
  main().catch(console.error);
}

export { main, monitorTask, cleanupExample };
