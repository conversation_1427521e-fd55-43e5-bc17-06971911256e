-- 套利任务主表
CREATE TABLE IF NOT EXISTS task (
    id SERIAL PRIMARY KEY,
    
    -- 基本信息
    task_id VARCHAR(50) UNIQUE NOT NULL,  -- 唯一任务ID (时间戳_随机字符串)
    strategy VARCHAR(20) NOT NULL,        -- 策略类型: 'cex_to_dex' 或 'dex_to_cex'
    current_step VARCHAR(30) NOT NULL,    -- 当前步骤
    status VARCHAR(20) NOT NULL DEFAULT 'running', -- 任务状态: 'running', 'completed', 'failed', 'timeout'
    
    -- 交易参数
    usdt_amount DECIMAL(18,6) NOT NULL,   -- 交易金额 (USDT)
    
    -- 价格信息 (创建时记录)
    cex_sol_price DECIMAL(18,6),          -- CEX SOL价格
    cex_iotx_price DECIMAL(18,6),         -- CEX IOTX价格
    dex_sol_price DECIMAL(18,6),          -- DEX SOL价格  
    dex_iotx_price DECIMAL(18,6),         -- DEX IOTX价格
    
    -- 钱包地址
    iotex_address VARCHAR(100),           -- IoTeX钱包地址
    solana_address VARCHAR(100),          -- Solana钱包地址
    
    -- 重试机制
    retry_count INTEGER DEFAULT 0,        -- 重试次数
    max_retries INTEGER DEFAULT 3,        -- 最大重试次数
    last_error TEXT,                      -- 最后一次错误信息
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,               -- 完成时间
    
    -- 索引字段
    INDEX idx_task_id (task_id),
    INDEX idx_strategy_status (strategy, status),
    INDEX idx_created_at (created_at)
);

-- 套利任务详情表 (记录每个步骤的详细数据)
CREATE TABLE IF NOT EXISTS task_detail (
    id SERIAL PRIMARY KEY,
    
    -- 关联信息
    task_id VARCHAR(50) NOT NULL,         -- 关联主表的task_id
    step_name VARCHAR(30) NOT NULL,       -- 步骤名称
    step_order INTEGER NOT NULL,          -- 步骤顺序
    
    -- 步骤状态
    status VARCHAR(20) NOT NULL DEFAULT 'pending', -- 步骤状态: 'pending', 'running', 'completed', 'failed'
    
    -- 交易数据 (JSON格式存储具体的交易信息)
    buy_order_data JSONB,                 -- 买单数据
    sell_order_data JSONB,                -- 卖单数据
    swap_result_data JSONB,               -- 交换结果数据
    bridge_result_data JSONB,             -- 跨链结果数据
    transfer_data JSONB,                  -- 转账数据
    balance_data JSONB,                   -- 余额数据
    
    -- 执行结果
    execution_result JSONB,               -- 执行结果 (成功时的返回数据)
    error_message TEXT,                   -- 错误信息
    transaction_hash VARCHAR(100),        -- 交易哈希
    
    -- 金额信息
    input_amount DECIMAL(18,6),           -- 输入金额
    output_amount DECIMAL(18,6),          -- 输出金额
    fee_amount DECIMAL(18,6),             -- 手续费
    
    -- 时间戳
    started_at TIMESTAMP,                 -- 步骤开始时间
    completed_at TIMESTAMP,               -- 步骤完成时间
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    FOREIGN KEY (task_id) REFERENCES task(task_id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_task_step (task_id, step_name),
    INDEX idx_task_order (task_id, step_order),
    INDEX idx_status (status)
);

-- 更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为主表添加更新时间触发器
CREATE TRIGGER update_task_updated_at 
    BEFORE UPDATE ON task 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 为详情表添加更新时间触发器  
CREATE TRIGGER update_task_detail_updated_at 
    BEFORE UPDATE ON task_detail 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 创建视图：获取任务及其当前步骤信息
CREATE OR REPLACE VIEW task_with_current_step AS
SELECT 
    t.*,
    td.step_name as current_step_name,
    td.step_order as current_step_order,
    td.status as current_step_status,
    td.error_message as current_step_error,
    td.started_at as current_step_started_at
FROM task t
LEFT JOIN task_detail td ON t.task_id = td.task_id 
    AND td.step_name = t.current_step
ORDER BY t.created_at DESC;

-- 创建视图：获取任务进度统计
CREATE OR REPLACE VIEW task_progress AS
SELECT 
    t.task_id,
    t.strategy,
    t.status,
    t.usdt_amount,
    COUNT(td.id) as total_steps,
    COUNT(CASE WHEN td.status = 'completed' THEN 1 END) as completed_steps,
    COUNT(CASE WHEN td.status = 'failed' THEN 1 END) as failed_steps,
    ROUND(
        COUNT(CASE WHEN td.status = 'completed' THEN 1 END) * 100.0 / 
        NULLIF(COUNT(td.id), 0), 2
    ) as progress_percentage,
    t.created_at,
    t.updated_at
FROM task t
LEFT JOIN task_detail td ON t.task_id = td.task_id
GROUP BY t.task_id, t.strategy, t.status, t.usdt_amount, t.created_at, t.updated_at
ORDER BY t.created_at DESC;
