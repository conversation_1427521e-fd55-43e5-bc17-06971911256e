import { ArbitrageService } from "./arbitrage";
import { TaskManager } from "./task-manager";
import { CexService } from "./cex";
import { MimoService } from "./mimo";
import { IoTubeBridgeService } from "./iotube";
import { SolanaService } from "./solana";
import {
  ArbitrageStrategy,
  ArbitrageStep,
  TaskStatus,
  StepStatus,
  type TaskEntity
} from "../tools/task";
// @ts-ignore
import { from } from "@iotexproject/iotex-address-ts";

/**
 * 基于数据库的套利服务
 * 提供流程记录、恢复和状态管理功能
 */
export class ArbitrageDbService {
  private arbitrageService: ArbitrageService;
  private taskManager: TaskManager;
  private cexService: CexService;
  private mimoService: MimoService;
  private bridgeService: IoTubeBridgeService;
  private solanaService: SolanaService;

  constructor(
    cexService: CexService,
    mimoService: MimoService,
    bridgeService: IoTubeBridgeService,
    solanaService: SolanaService,
    config: {
      maxTradeAmount: number;
      walletAddresses: {
        iotex: string;
        solana: string;
      };
    }
  ) {
    this.cexService = cexService;
    this.mimoService = mimoService;
    this.bridgeService = bridgeService;
    this.solanaService = solanaService;

    this.arbitrageService = new ArbitrageService(
      cexService,
      mimoService,
      bridgeService,
      solanaService,
      config
    );

    this.taskManager = new TaskManager();
  }

  /**
   * 创建新的套利任务
   */
  async createArbitrageTask(
    strategy: ArbitrageStrategy,
    usdtAmount: number,
    priceInfo?: {
      cexSolPrice?: number;
      cexIotxPrice?: number;
      dexSolPrice?: number;
      dexIotxPrice?: number;
    }
  ): Promise<string> {
    console.log(`🆕 创建新套利任务: ${strategy}, 金额: $${usdtAmount}`);
    
    // 获取当前价格信息（如果未提供）
    const currentPrices = priceInfo || {};

    const taskId = await this.taskManager.createTask(strategy, usdtAmount, {
      iotexAddress: this.arbitrageService['config'].walletAddresses.iotex,
      solanaAddress: this.arbitrageService['config'].walletAddresses.solana,
      ...currentPrices
    });

    console.log(`✅ 任务创建成功: ${taskId}`);
    return taskId;
  }

  /**
   * 执行套利任务（新任务或恢复任务）
   */
  async executeArbitrageTask(taskId?: string, strategy?: ArbitrageStrategy, usdtAmount?: number): Promise<any> {
    let currentTaskId = taskId;
    
    // 如果没有提供 taskId，检查是否有未完成的任务
    if (!currentTaskId && strategy) {
      const incompleteTask = await this.taskManager.findIncompleteTask(strategy);
      if (incompleteTask) {
        currentTaskId = incompleteTask.task_id;
        console.log(`🔄 发现未完成任务，继续执行: ${currentTaskId}`);
      } else if (usdtAmount) {
        // 创建新任务
        currentTaskId = await this.createArbitrageTask(strategy, usdtAmount);
      } else {
        throw new Error("需要提供 usdtAmount 来创建新任务");
      }
    }

    if (!currentTaskId) {
      throw new Error("无法确定要执行的任务");
    }

    try {
      const taskInfo = await this.taskManager.getTaskWithSteps(currentTaskId);
      if (!taskInfo) {
        throw new Error(`任务 ${currentTaskId} 不存在`);
      }

      const { task } = taskInfo;
      console.log(`📋 开始执行任务 ${currentTaskId}，策略: ${task.strategy}，当前步骤: ${task.current_step}`);

      if (task.strategy === ArbitrageStrategy.CEX_TO_DEX) {
        return await this.executeCexToDexWithDb(currentTaskId, task);
      } else {
        return await this.executeDexToCexWithDb(currentTaskId, task);
      }

    } catch (error) {
      console.error(`❌ 任务 ${currentTaskId} 执行失败:`, error);
      await this.taskManager.failTask(currentTaskId, error instanceof Error ? error.message : String(error));
      throw error;
    }
  }

  /**
   * 执行 CEX -> DEX 策略（带数据库记录和步骤级恢复）
   */
  private async executeCexToDexWithDb(taskId: string, task: TaskEntity): Promise<any> {
    console.log(`🚀 执行 CEX -> DEX 套利策略，任务ID: ${taskId}，当前步骤: ${task.current_step}`);

    const usdtAmount = task.usdt_amount;
    let stepData: any = {};

    try {
      // 执行 CEX -> DEX 步骤序列
      stepData = await this.executeCexToDexSteps(taskId, task, usdtAmount);

      // 标记任务完成
      await this.taskManager.completeTask(taskId, stepData);

      console.log(`💰 CEX -> DEX 套利完成，任务ID: ${taskId}`);
      return {
        taskId,
        strategy: "cex_to_dex",
        status: "completed",
        finalData: stepData
      };

    } catch (error) {
      console.error(`❌ CEX -> DEX 套利失败，任务ID: ${taskId}:`, error);
      await this.taskManager.failTask(taskId, error instanceof Error ? error.message : String(error));
      throw error;
    }
  }



  /**
   * 执行 DEX -> CEX 策略（带数据库记录和步骤级恢复）
   */
  private async executeDexToCexWithDb(taskId: string, task: TaskEntity): Promise<any> {
    console.log(`🚀 执行 DEX -> CEX 套利策略，任务ID: ${taskId}，当前步骤: ${task.current_step}`);

    try {
      // 执行 DEX -> CEX 步骤序列
      const stepData = await this.executeDexToCexSteps(taskId, task, task.usdt_amount);

      // 标记任务完成
      await this.taskManager.completeTask(taskId, stepData);

      console.log(`💰 DEX -> CEX 套利完成，任务ID: ${taskId}`);
      return {
        taskId,
        strategy: "dex_to_cex",
        status: "completed",
        finalData: stepData
      };

    } catch (error) {
      console.error(`❌ DEX -> CEX 套利失败，任务ID: ${taskId}:`, error);
      await this.taskManager.failTask(taskId, error instanceof Error ? error.message : String(error));
      throw error;
    }
  }



  /**
   * 获取任务状态
   */
  async getTaskStatus(taskId: string): Promise<{
    task: TaskEntity;
    steps: any[];
    progress: number;
  } | null> {
    const taskInfo = await this.taskManager.getTaskWithSteps(taskId);
    if (!taskInfo) {
      return null;
    }

    const { task, steps } = taskInfo;
    const completedSteps = steps.filter(s => s.status === StepStatus.COMPLETED).length;
    const progress = steps.length > 0 ? (completedSteps / steps.length) * 100 : 0;

    return {
      task,
      steps: steps.map(step => ({
        name: step.step_name,
        status: step.status,
        order: step.step_order,
        startedAt: step.started_at,
        completedAt: step.completed_at,
        errorMessage: step.error_message,
        executionResult: step.execution_result
      })),
      progress
    };
  }

  /**
   * 获取所有运行中的任务
   */
  async getRunningTasks(): Promise<TaskEntity[]> {
    const cexToDexTasks = await this.taskManager.findIncompleteTask(ArbitrageStrategy.CEX_TO_DEX);
    const dexToCexTasks = await this.taskManager.findIncompleteTask(ArbitrageStrategy.DEX_TO_CEX);

    const tasks = [];
    if (cexToDexTasks) tasks.push(cexToDexTasks);
    if (dexToCexTasks) tasks.push(dexToCexTasks);

    return tasks;
  }

  /**
   * 重试失败的任务
   */
  async retryFailedTask(taskId: string): Promise<any> {
    const taskInfo = await this.taskManager.getTaskWithSteps(taskId);
    if (!taskInfo) {
      throw new Error(`任务 ${taskId} 不存在`);
    }

    const { task } = taskInfo;
    if (task.status !== TaskStatus.FAILED) {
      throw new Error(`任务 ${taskId} 状态不是失败状态，无法重试`);
    }

    if (task.retry_count >= task.max_retries) {
      throw new Error(`任务 ${taskId} 已达到最大重试次数 ${task.max_retries}`);
    }

    console.log(`🔄 重试失败任务: ${taskId}，当前重试次数: ${task.retry_count}`);

    // 重置任务状态为运行中，通过 TaskManager 的公共方法
    await this.taskManager.updateTaskStep(taskId, task.current_step);

    // 重新执行任务
    return await this.executeArbitrageTask(taskId);
  }

  /**
   * 取消任务
   */
  async cancelTask(taskId: string): Promise<void> {
    const taskInfo = await this.taskManager.getTaskWithSteps(taskId);
    if (!taskInfo) {
      throw new Error(`任务 ${taskId} 不存在`);
    }

    const { task } = taskInfo;
    if (task.status === TaskStatus.COMPLETED) {
      throw new Error(`任务 ${taskId} 已完成，无法取消`);
    }

    console.log(`❌ 取消任务: ${taskId}`);
    await this.taskManager.failTask(taskId, "任务被用户取消");
  }

  /**
   * 获取任务执行报告
   */
  async getTaskReport(taskId: string): Promise<any> {
    const result = await this.getTaskStatus(taskId);
    if (!result) {
      throw new Error(`任务 ${taskId} 不存在`);
    }

    const { task, steps } = result;

    // 计算执行时间
    const startTime = task.created_at ? new Date(task.created_at).getTime() : 0;
    const endTime = task.completed_at ? new Date(task.completed_at).getTime() : Date.now();
    const executionTime = Math.floor((endTime - startTime) / 1000); // 秒

    // 统计步骤状态
    const stepStats = {
      total: steps.length,
      completed: steps.filter(s => s.status === StepStatus.COMPLETED).length,
      failed: steps.filter(s => s.status === StepStatus.FAILED).length,
      running: steps.filter(s => s.status === StepStatus.RUNNING).length,
      pending: steps.filter(s => s.status === StepStatus.PENDING).length
    };

    return {
      taskId,
      strategy: task.strategy,
      status: task.status,
      currentStep: task.current_step,
      usdtAmount: task.usdt_amount,
      retryCount: task.retry_count,
      maxRetries: task.max_retries,
      executionTime: `${Math.floor(executionTime / 60)}分${executionTime % 60}秒`,
      stepStats,
      steps,
      createdAt: task.created_at,
      completedAt: task.completed_at,
      lastError: task.last_error
    };
  }

  /**
   * 清理已完成的旧任务（可选的维护功能）
   */
  async cleanupOldTasks(daysOld: number = 30): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    console.log(`🧹 清理 ${daysOld} 天前的已完成任务...`);

    // 这里需要实现具体的清理逻辑
    // 由于当前的 PostgresAdapter 没有提供批量删除方法，这里只是示例
    console.log(`清理截止日期: ${cutoffDate.toISOString()}`);

    // 实际实现需要添加到 TaskAdapter 中
    return 0; // 返回清理的任务数量
  }

  /**
   * 获取指定步骤的执行数据
   */
  private async getStepData(taskId: string, step: ArbitrageStep): Promise<any> {
    const taskInfo = await this.taskManager.getTaskWithSteps(taskId);
    if (!taskInfo) return {};

    const stepDetail = taskInfo.steps.find(s => s.step_name === step);
    return stepDetail?.execution_result || {};
  }

  /**
   * 步骤 1: 在 CEX 购买 IOTX
   */
  private async executeBuyIotxStep(taskId: string, usdtAmount: number): Promise<any> {
    console.log("步骤 1: 在 CEX 上买 IOTX");
    await this.taskManager.updateTaskStep(taskId, ArbitrageStep.BUY_TOKEN);

    const buyOrder = await this.cexService.buyIotx(usdtAmount);
    await this.taskManager.updateTaskStep(taskId, ArbitrageStep.WITHDRAW_TOKEN, { buyOrder });

    await new Promise(resolve => setTimeout(resolve, 10000));
    return { buyOrder };
  }

  /**
   * 步骤 2: 将 IOTX 提现到 IoTeX 钱包
   */
  private async executeWithdrawIotxStep(taskId: string, buyOrder: any, iotexAddress: string): Promise<any> {
    console.log("步骤 2: 将 IOTX 提现到 IoTeX 钱包");

    const iotxAmount = Math.floor(buyOrder.filled || buyOrder.amount);
    await this.cexService.withdrawIotx(iotxAmount, iotexAddress);
    await this.arbitrageService['waitForIotxBalanceIncrease'](iotxAmount);
    await this.taskManager.updateTaskStep(taskId, ArbitrageStep.SWAP_TOKEN, { iotxAmount });

    return { buyOrder, iotxAmount };
  }

  /**
   * 步骤 3: 在 MIMO 将 IOTX 换成 SOL
   */
  private async executeSwapIotxToSolStep(taskId: string, buyOrder: any): Promise<any> {
    console.log("步骤 3: 在 MIMO 将 IOTX 换成 SOL");

    const iotxAmountForSwap = Math.floor(buyOrder.filled || buyOrder.amount);
    const swapResult = await this.mimoService.swapIotxToSol(iotxAmountForSwap);
    await this.taskManager.updateTaskStep(taskId, ArbitrageStep.BRIDGE_TRANSFER, { swapResult });

    return { buyOrder, swapResult };
  }

  /**
   * 步骤 4: 将 SOL 通过跨链桥转移到 Solana
   */
  private async executeBridgeToSolanaStep(taskId: string, swapResult: any, solanaAddress: string): Promise<any> {
    console.log("步骤 4: 将 SOL 通过跨链桥转移到 Solana");

    const bridgeResult = await this.bridgeService.transferSolFromIotexToSolana(
      swapResult.solAmount,
      solanaAddress
    );
    await this.arbitrageService['waitForSolanaWsolBalanceIncrease'](bridgeResult.receivedAmount, 1000 * 60 * 10);
    await this.taskManager.updateTaskStep(taskId, ArbitrageStep.UNWRAP_SOL, { bridgeResult });

    return { swapResult, bridgeResult };
  }

  /**
   * 步骤 5: 解包 wSOL
   */
  private async executeUnwrapSolStep(taskId: string): Promise<any> {
    console.log("步骤 5: 解包 wSOL");

    const solanaService = new SolanaService({
      rpcUrl: this.bridgeService.config.solanaRpcUrl,
      privateKey: this.bridgeService.config.solanaPrivateKey,
    });

    await solanaService.unwrapSol();
    await this.taskManager.updateTaskStep(taskId, ArbitrageStep.TRANSFER_TO_CEX);

    return {};
  }

  /**
   * 步骤 6: 转账 SOL 到 CEX
   */
  private async executeTransferSolToCexStep(taskId: string, swapResult: any): Promise<any> {
    console.log("步骤 6: 转账 SOL 到 CEX");

    const solanaServiceForTransfer = new SolanaService({
      rpcUrl: this.bridgeService.config.solanaRpcUrl,
      privateKey: this.bridgeService.config.solanaPrivateKey,
    });

    const bnDepositSolAddress = await this.cexService.getSolDepositAddress();
    const transferTx = await solanaServiceForTransfer.transfer(bnDepositSolAddress, swapResult.solAmount);

    await this.cexService.waitForSolBalanceIncrease(swapResult.solAmount);
    await this.taskManager.updateTaskStep(taskId, ArbitrageStep.SELL_TOKEN, { transferTx });

    return { swapResult, transferTx };
  }

  /**
   * 步骤 7: 在 CEX 卖出 SOL
   */
  private async executeSellSolStep(taskId: string, swapResult: any): Promise<any> {
    console.log("步骤 7: 在 CEX 卖出 SOL");

    const sellOrder = await this.cexService.sellSymbol("SOLUSDT", swapResult.solAmount);

    return { swapResult, sellOrder };
  }

  /**
   * 执行 CEX -> DEX 步骤序列（支持从任意步骤恢复）
   */
  private async executeCexToDexSteps(taskId: string, task: TaskEntity, usdtAmount: number): Promise<any> {
    let stepData: any = {};

    // 定义步骤执行顺序
    const steps = [
      ArbitrageStep.INIT,
      ArbitrageStep.BUY_TOKEN,
      ArbitrageStep.WITHDRAW_TOKEN,
      ArbitrageStep.SWAP_TOKEN,
      ArbitrageStep.BRIDGE_TRANSFER,
      ArbitrageStep.UNWRAP_SOL,
      ArbitrageStep.TRANSFER_TO_CEX,
      ArbitrageStep.SELL_TOKEN
    ];

    // 找到当前步骤的索引
    const currentStepIndex = steps.indexOf(task.current_step);
    if (currentStepIndex === -1) {
      throw new Error(`未知的当前步骤: ${task.current_step}`);
    }

    // 从当前步骤开始执行
    for (let i = currentStepIndex; i < steps.length; i++) {
      const step = steps[i];

      switch (step) {
        case ArbitrageStep.INIT:
        case ArbitrageStep.BUY_TOKEN:
          if (task.current_step === ArbitrageStep.INIT || task.current_step === ArbitrageStep.BUY_TOKEN) {
            stepData = await this.executeBuyIotxStep(taskId, usdtAmount);
          }
          break;

        case ArbitrageStep.WITHDRAW_TOKEN:
          if (task.current_step === ArbitrageStep.WITHDRAW_TOKEN) {
            if (!stepData.buyOrder) {
              const buyData = await this.getStepData(taskId, ArbitrageStep.BUY_TOKEN);
              stepData.buyOrder = buyData.buyOrder;
            }
            stepData = await this.executeWithdrawIotxStep(taskId, stepData.buyOrder, task.iotex_address!);
          }
          break;

        case ArbitrageStep.SWAP_TOKEN:
          if (task.current_step === ArbitrageStep.SWAP_TOKEN) {
            if (!stepData.buyOrder) {
              const buyData = await this.getStepData(taskId, ArbitrageStep.BUY_TOKEN);
              stepData.buyOrder = buyData.buyOrder;
            }
            stepData = await this.executeSwapIotxToSolStep(taskId, stepData.buyOrder);
          }
          break;

        case ArbitrageStep.BRIDGE_TRANSFER:
          if (task.current_step === ArbitrageStep.BRIDGE_TRANSFER) {
            if (!stepData.swapResult) {
              const swapData = await this.getStepData(taskId, ArbitrageStep.SWAP_TOKEN);
              stepData.swapResult = swapData.swapResult;
            }
            stepData = await this.executeBridgeToSolanaStep(taskId, stepData.swapResult, task.solana_address!);
          }
          break;

        case ArbitrageStep.UNWRAP_SOL:
          if (task.current_step === ArbitrageStep.UNWRAP_SOL) {
            stepData = await this.executeUnwrapSolStep(taskId);
          }
          break;

        case ArbitrageStep.TRANSFER_TO_CEX:
          if (task.current_step === ArbitrageStep.TRANSFER_TO_CEX) {
            if (!stepData.swapResult) {
              const swapData = await this.getStepData(taskId, ArbitrageStep.SWAP_TOKEN);
              stepData.swapResult = swapData.swapResult;
            }
            stepData = await this.executeTransferSolToCexStep(taskId, stepData.swapResult);
          }
          break;

        case ArbitrageStep.SELL_TOKEN:
          if (task.current_step === ArbitrageStep.SELL_TOKEN) {
            if (!stepData.swapResult) {
              const swapData = await this.getStepData(taskId, ArbitrageStep.SWAP_TOKEN);
              stepData.swapResult = swapData.swapResult;
            }
            stepData = await this.executeSellSolStep(taskId, stepData.swapResult);
          }
          break;
      }
    }

    return stepData;
  }

  /**
   * 执行 DEX -> CEX 步骤序列（支持从任意步骤恢复）
   */
  private async executeDexToCexSteps(taskId: string, task: TaskEntity, usdtAmount: number): Promise<any> {
    let stepData: any = {};

    // 定义步骤执行顺序
    const steps = [
      ArbitrageStep.INIT,
      ArbitrageStep.BUY_TOKEN,
      ArbitrageStep.WITHDRAW_TOKEN,
      ArbitrageStep.BRIDGE_TRANSFER,
      ArbitrageStep.SWAP_TOKEN,
      ArbitrageStep.TRANSFER_TO_CEX,
      ArbitrageStep.SELL_TOKEN
    ];

    // 找到当前步骤的索引
    const currentStepIndex = steps.indexOf(task.current_step);
    if (currentStepIndex === -1) {
      throw new Error(`未知的当前步骤: ${task.current_step}`);
    }

    // 从当前步骤开始执行
    for (let i = currentStepIndex; i < steps.length; i++) {
      const step = steps[i];

      switch (step) {
        case ArbitrageStep.INIT:
        case ArbitrageStep.BUY_TOKEN:
          if (task.current_step === ArbitrageStep.INIT || task.current_step === ArbitrageStep.BUY_TOKEN) {
            stepData = await this.executeBuySolStep(taskId, usdtAmount);
          }
          break;

        case ArbitrageStep.WITHDRAW_TOKEN:
          if (task.current_step === ArbitrageStep.WITHDRAW_TOKEN) {
            if (!stepData.buyOrder) {
              const buyData = await this.getStepData(taskId, ArbitrageStep.BUY_TOKEN);
              stepData.buyOrder = buyData.buyOrder;
            }
            stepData = await this.executeWithdrawSolStep(taskId, stepData.buyOrder, task.solana_address!);
          }
          break;

        case ArbitrageStep.BRIDGE_TRANSFER:
          if (task.current_step === ArbitrageStep.BRIDGE_TRANSFER) {
            if (!stepData.buyOrder) {
              const buyData = await this.getStepData(taskId, ArbitrageStep.BUY_TOKEN);
              stepData.buyOrder = buyData.buyOrder;
            }
            stepData = await this.executeBridgeToIotexStep(taskId, stepData.buyOrder, task.iotex_address!);
          }
          break;

        case ArbitrageStep.SWAP_TOKEN:
          if (task.current_step === ArbitrageStep.SWAP_TOKEN) {
            if (!stepData.bridgeResult) {
              const bridgeData = await this.getStepData(taskId, ArbitrageStep.BRIDGE_TRANSFER);
              stepData.bridgeResult = bridgeData.bridgeResult;
            }
            stepData = await this.executeSwapSolToIotxStep(taskId, stepData.bridgeResult);
          }
          break;

        case ArbitrageStep.TRANSFER_TO_CEX:
          if (task.current_step === ArbitrageStep.TRANSFER_TO_CEX) {
            if (!stepData.swapResult) {
              const swapData = await this.getStepData(taskId, ArbitrageStep.SWAP_TOKEN);
              stepData.swapResult = swapData.swapResult;
            }
            stepData = await this.executeTransferIotxToCexStep(taskId, stepData.swapResult);
          }
          break;

        case ArbitrageStep.SELL_TOKEN:
          if (task.current_step === ArbitrageStep.SELL_TOKEN) {
            if (!stepData.swapResult) {
              const swapData = await this.getStepData(taskId, ArbitrageStep.SWAP_TOKEN);
              stepData.swapResult = swapData.swapResult;
            }
            stepData = await this.executeSellIotxStep(taskId, stepData.swapResult);
          }
          break;
      }
    }

    return stepData;
  }

  /**
   * DEX -> CEX: 步骤 1 - 在 CEX 购买 SOL
   */
  private async executeBuySolStep(taskId: string, usdtAmount: number): Promise<any> {
    console.log("步骤 1: 在 CEX 上买 SOL");
    await this.taskManager.updateTaskStep(taskId, ArbitrageStep.BUY_TOKEN);

    // 预检查最小提现要求
    const minWithdrawAmount = 0.1;
    const solPrice = await this.cexService.getSolPrice();
    const estimatedSolAmount = usdtAmount / solPrice;

    if (estimatedSolAmount < minWithdrawAmount) {
      throw new Error(`预估 SOL 购买数量 ${estimatedSolAmount.toFixed(4)} 小于最小提现要求 ${minWithdrawAmount} SOL`);
    }

    const buyOrder = await this.cexService.buySol(usdtAmount);
    const solAmount = buyOrder.filled || buyOrder.amount;

    if (solAmount < minWithdrawAmount) {
      throw new Error(`实际 SOL 购买数量 ${solAmount} 小于最小提现要求 ${minWithdrawAmount} SOL`);
    }

    await this.taskManager.updateTaskStep(taskId, ArbitrageStep.WITHDRAW_TOKEN, { buyOrder, solAmount });
    await new Promise(resolve => setTimeout(resolve, 5000));

    return { buyOrder };
  }

  /**
   * DEX -> CEX: 步骤 2 - 将 SOL 提现到 Solana 钱包
   */
  private async executeWithdrawSolStep(taskId: string, buyOrder: any, solanaAddress: string): Promise<any> {
    console.log("步骤 2: 将 SOL 提现到 Solana 钱包");

    const solAmountToWithdraw = buyOrder.filled || buyOrder.amount;
    await this.cexService.withdrawSol(solAmountToWithdraw, solanaAddress);
    await this.arbitrageService['waitForSolanaBalanceIncrease'](solAmountToWithdraw);
    await this.taskManager.updateTaskStep(taskId, ArbitrageStep.BRIDGE_TRANSFER, { solAmountToWithdraw });

    return { buyOrder, solAmountToWithdraw };
  }

  /**
   * DEX -> CEX: 步骤 3 - 通过跨链桥将 SOL 转移到 IoTeX
   */
  private async executeBridgeToIotexStep(taskId: string, buyOrder: any, iotexAddress: string): Promise<any> {
    console.log("步骤 3: 通过跨链桥将 SOL 转移到 IoTeX");

    const solAmountForBridge = buyOrder.filled || buyOrder.amount;
    const bridgeResult = await this.bridgeService.transferSolFromSolanaToIotex(
      solAmountForBridge,
      iotexAddress
    );
    await this.arbitrageService['waitForSolBalanceIncrease'](bridgeResult.receivedAmount);
    await this.taskManager.updateTaskStep(taskId, ArbitrageStep.SWAP_TOKEN, { bridgeResult });

    return { buyOrder, bridgeResult };
  }

  /**
   * DEX -> CEX: 步骤 4 - 在 MIMO 上将 SOL 转换为 IOTX
   */
  private async executeSwapSolToIotxStep(taskId: string, bridgeResult: any): Promise<any> {
    console.log("步骤 4: 在 MIMO 上将 SOL 转换为 IOTX");

    const swapResult = await this.mimoService.swapSolToIotx(bridgeResult.receivedAmount);
    await this.taskManager.updateTaskStep(taskId, ArbitrageStep.TRANSFER_TO_CEX, { swapResult });

    return { bridgeResult, swapResult };
  }

  /**
   * DEX -> CEX: 步骤 5 - 将 IOTX 充值到 CEX
   */
  private async executeTransferIotxToCexStep(taskId: string, swapResult: any): Promise<any> {
    console.log("步骤 5: 将 IOTX 充值到 CEX");

    const depositAddress = await this.cexService.getIotxDepositAddress();
    const transferHash = await this.mimoService.sendIotx(
      swapResult.iotxAmount,
      from(depositAddress).stringEth()
    );

    await this.cexService.waitForIotxBalanceIncrease(swapResult.iotxAmount);
    await this.taskManager.updateTaskStep(taskId, ArbitrageStep.SELL_TOKEN, { transferHash });

    return { swapResult, transferHash };
  }

  /**
   * DEX -> CEX: 步骤 6 - 在 CEX 卖出 IOTX
   */
  private async executeSellIotxStep(taskId: string, swapResult: any): Promise<any> {
    console.log("步骤 6: 在 CEX 卖出 IOTX");

    const sellOrder = await this.cexService.sellIotx(swapResult.iotxAmount);

    return { swapResult, sellOrder };
  }
}
