import { ArbitrageService } from "./arbitrage";
import { TaskManager } from "./task-manager";
import { CexService } from "./cex";
import { MimoService } from "./mimo";
import { IoTubeBridgeService } from "./iotube";
import { SolanaService } from "./solana";
import {
  ArbitrageStrategy,
  TaskStatus,
  StepStatus,
  type TaskEntity
} from "../tools/task";

/**
 * 基于数据库的套利服务
 * 提供流程记录、恢复和状态管理功能
 */
export class ArbitrageDbService {
  private arbitrageService: ArbitrageService;
  private taskManager: TaskManager;

  constructor(
    cexService: CexService,
    mimoService: MimoService,
    bridgeService: IoTubeBridgeService,
    solanaService: SolanaService,
    config: {
      maxTradeAmount: number;
      walletAddresses: {
        iotex: string;
        solana: string;
      };
    }
  ) {
    this.arbitrageService = new ArbitrageService(
      cexService,
      mimoService,
      bridgeService,
      solanaService,
      config
    );

    this.taskManager = new TaskManager();
  }

  /**
   * 创建新的套利任务
   */
  async createArbitrageTask(
    strategy: ArbitrageStrategy,
    usdtAmount: number,
    priceInfo?: {
      cexSolPrice?: number;
      cexIotxPrice?: number;
      dexSolPrice?: number;
      dexIotxPrice?: number;
    }
  ): Promise<string> {
    console.log(`🆕 创建新套利任务: ${strategy}, 金额: $${usdtAmount}`);
    
    // 获取当前价格信息（如果未提供）
    const currentPrices = priceInfo || {};

    const taskId = await this.taskManager.createTask(strategy, usdtAmount, {
      iotexAddress: this.arbitrageService['config'].walletAddresses.iotex,
      solanaAddress: this.arbitrageService['config'].walletAddresses.solana,
      ...currentPrices
    });

    console.log(`✅ 任务创建成功: ${taskId}`);
    return taskId;
  }

  /**
   * 执行套利任务（新任务或恢复任务）
   */
  async executeArbitrageTask(taskId?: string, strategy?: ArbitrageStrategy, usdtAmount?: number): Promise<any> {
    let currentTaskId = taskId;
    
    // 如果没有提供 taskId，检查是否有未完成的任务
    if (!currentTaskId && strategy) {
      const incompleteTask = await this.taskManager.findIncompleteTask(strategy);
      if (incompleteTask) {
        currentTaskId = incompleteTask.task_id;
        console.log(`🔄 发现未完成任务，继续执行: ${currentTaskId}`);
      } else if (usdtAmount) {
        // 创建新任务
        currentTaskId = await this.createArbitrageTask(strategy, usdtAmount);
      } else {
        throw new Error("需要提供 usdtAmount 来创建新任务");
      }
    }

    if (!currentTaskId) {
      throw new Error("无法确定要执行的任务");
    }

    try {
      const taskInfo = await this.taskManager.getTaskWithSteps(currentTaskId);
      if (!taskInfo) {
        throw new Error(`任务 ${currentTaskId} 不存在`);
      }

      const { task } = taskInfo;
      console.log(`📋 开始执行任务 ${currentTaskId}，策略: ${task.strategy}，当前步骤: ${task.current_step}`);

      if (task.strategy === ArbitrageStrategy.CEX_TO_DEX) {
        return await this.executeCexToDexWithDb(currentTaskId, task);
      } else {
        return await this.executeDexToCexWithDb(currentTaskId, task);
      }

    } catch (error) {
      console.error(`❌ 任务 ${currentTaskId} 执行失败:`, error);
      await this.taskManager.failTask(currentTaskId, error instanceof Error ? error.message : String(error));
      throw error;
    }
  }

  /**
   * 执行 CEX -> DEX 策略（带数据库记录）
   */
  private async executeCexToDexWithDb(taskId: string, task: TaskEntity): Promise<any> {
    console.log(`🚀 执行 CEX -> DEX 套利策略，任务ID: ${taskId}`);

    try {
      // 使用原始的 ArbitrageService 执行策略
      const result = await this.arbitrageService.execIotexPath(task.usdt_amount);

      // 更新任务为完成状态
      await this.taskManager.completeTask(taskId, result);

      console.log(`💰 CEX -> DEX 套利完成，任务ID: ${taskId}`);
      return {
        ...result,
        taskId,
        dbStatus: "completed"
      };

    } catch (error) {
      console.error(`❌ CEX -> DEX 套利失败，任务ID: ${taskId}:`, error);
      await this.taskManager.failTask(taskId, error instanceof Error ? error.message : String(error));
      throw error;
    }
  }



  /**
   * 执行 DEX -> CEX 策略（带数据库记录）
   */
  private async executeDexToCexWithDb(taskId: string, task: TaskEntity): Promise<any> {
    console.log(`🚀 执行 DEX -> CEX 套利策略，任务ID: ${taskId}`);

    try {
      // 使用原始的 ArbitrageService 执行策略
      const result = await this.arbitrageService.execSolPath(task.usdt_amount);

      // 更新任务为完成状态
      await this.taskManager.completeTask(taskId, result);

      console.log(`💰 DEX -> CEX 套利完成，任务ID: ${taskId}`);
      return {
        ...result,
        taskId,
        dbStatus: "completed"
      };

    } catch (error) {
      console.error(`❌ DEX -> CEX 套利失败，任务ID: ${taskId}:`, error);
      await this.taskManager.failTask(taskId, error instanceof Error ? error.message : String(error));
      throw error;
    }
  }



  /**
   * 获取任务状态
   */
  async getTaskStatus(taskId: string): Promise<{
    task: TaskEntity;
    steps: any[];
    progress: number;
  } | null> {
    const taskInfo = await this.taskManager.getTaskWithSteps(taskId);
    if (!taskInfo) {
      return null;
    }

    const { task, steps } = taskInfo;
    const completedSteps = steps.filter(s => s.status === StepStatus.COMPLETED).length;
    const progress = steps.length > 0 ? (completedSteps / steps.length) * 100 : 0;

    return {
      task,
      steps: steps.map(step => ({
        name: step.step_name,
        status: step.status,
        order: step.step_order,
        startedAt: step.started_at,
        completedAt: step.completed_at,
        errorMessage: step.error_message,
        executionResult: step.execution_result
      })),
      progress
    };
  }

  /**
   * 获取所有运行中的任务
   */
  async getRunningTasks(): Promise<TaskEntity[]> {
    const cexToDexTasks = await this.taskManager.findIncompleteTask(ArbitrageStrategy.CEX_TO_DEX);
    const dexToCexTasks = await this.taskManager.findIncompleteTask(ArbitrageStrategy.DEX_TO_CEX);

    const tasks = [];
    if (cexToDexTasks) tasks.push(cexToDexTasks);
    if (dexToCexTasks) tasks.push(dexToCexTasks);

    return tasks;
  }

  /**
   * 重试失败的任务
   */
  async retryFailedTask(taskId: string): Promise<any> {
    const taskInfo = await this.taskManager.getTaskWithSteps(taskId);
    if (!taskInfo) {
      throw new Error(`任务 ${taskId} 不存在`);
    }

    const { task } = taskInfo;
    if (task.status !== TaskStatus.FAILED) {
      throw new Error(`任务 ${taskId} 状态不是失败状态，无法重试`);
    }

    if (task.retry_count >= task.max_retries) {
      throw new Error(`任务 ${taskId} 已达到最大重试次数 ${task.max_retries}`);
    }

    console.log(`🔄 重试失败任务: ${taskId}，当前重试次数: ${task.retry_count}`);

    // 重置任务状态为运行中，通过 TaskManager 的公共方法
    await this.taskManager.updateTaskStep(taskId, task.current_step);

    // 重新执行任务
    return await this.executeArbitrageTask(taskId);
  }

  /**
   * 取消任务
   */
  async cancelTask(taskId: string): Promise<void> {
    const taskInfo = await this.taskManager.getTaskWithSteps(taskId);
    if (!taskInfo) {
      throw new Error(`任务 ${taskId} 不存在`);
    }

    const { task } = taskInfo;
    if (task.status === TaskStatus.COMPLETED) {
      throw new Error(`任务 ${taskId} 已完成，无法取消`);
    }

    console.log(`❌ 取消任务: ${taskId}`);
    await this.taskManager.failTask(taskId, "任务被用户取消");
  }

  /**
   * 获取任务执行报告
   */
  async getTaskReport(taskId: string): Promise<any> {
    const result = await this.getTaskStatus(taskId);
    if (!result) {
      throw new Error(`任务 ${taskId} 不存在`);
    }

    const { task, steps } = result;

    // 计算执行时间
    const startTime = task.created_at ? new Date(task.created_at).getTime() : 0;
    const endTime = task.completed_at ? new Date(task.completed_at).getTime() : Date.now();
    const executionTime = Math.floor((endTime - startTime) / 1000); // 秒

    // 统计步骤状态
    const stepStats = {
      total: steps.length,
      completed: steps.filter(s => s.status === StepStatus.COMPLETED).length,
      failed: steps.filter(s => s.status === StepStatus.FAILED).length,
      running: steps.filter(s => s.status === StepStatus.RUNNING).length,
      pending: steps.filter(s => s.status === StepStatus.PENDING).length
    };

    return {
      taskId,
      strategy: task.strategy,
      status: task.status,
      currentStep: task.current_step,
      usdtAmount: task.usdt_amount,
      retryCount: task.retry_count,
      maxRetries: task.max_retries,
      executionTime: `${Math.floor(executionTime / 60)}分${executionTime % 60}秒`,
      stepStats,
      steps,
      createdAt: task.created_at,
      completedAt: task.completed_at,
      lastError: task.last_error
    };
  }

  /**
   * 清理已完成的旧任务（可选的维护功能）
   */
  async cleanupOldTasks(daysOld: number = 30): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    console.log(`🧹 清理 ${daysOld} 天前的已完成任务...`);

    // 这里需要实现具体的清理逻辑
    // 由于当前的 PostgresAdapter 没有提供批量删除方法，这里只是示例
    console.log(`清理截止日期: ${cutoffDate.toISOString()}`);

    // 实际实现需要添加到 TaskAdapter 中
    return 0; // 返回清理的任务数量
  }
}
