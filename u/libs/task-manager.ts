import { 
  TaskAdapter, 
  TaskDetailAdapter, 
  type TaskEntity, 
  type TaskDetailEntity,
  ArbitrageStrategy,
  ArbitrageStep,
  TaskStatus,
  StepStatus
} from "../tools/task";

/**
 * 任务管理服务
 * 负责套利任务的创建、更新、恢复等操作
 */
export class TaskManager {
  private taskAdapter: TaskAdapter;
  private taskDetailAdapter: TaskDetailAdapter;

  constructor() {
    this.taskAdapter = new TaskAdapter();
    this.taskDetailAdapter = new TaskDetailAdapter();
  }

  /**
   * 生成唯一的任务ID
   */
  private generateTaskId(): string {
    return `${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  /**
   * 创建新任务
   */
  async createTask(
    strategy: ArbitrageStrategy,
    usdtAmount: number,
    config: {
      iotexAddress: string;
      solanaAddress: string;
      cexSolPrice?: number;
      cexIotxPrice?: number;
      dexSolPrice?: number;
      dexIotxPrice?: number;
    }
  ): Promise<string> {
    const taskId = this.generateTaskId();
    
    // 创建主任务记录
    const task: Omit<TaskEntity, 'id'> = {
      task_id: taskId,
      strategy,
      current_step: ArbitrageStep.INIT,
      status: TaskStatus.RUNNING,
      usdt_amount: usdtAmount,
      cex_sol_price: config.cexSolPrice,
      cex_iotx_price: config.cexIotxPrice,
      dex_sol_price: config.dexSolPrice,
      dex_iotx_price: config.dexIotxPrice,
      iotex_address: config.iotexAddress,
      solana_address: config.solanaAddress,
      retry_count: 0,
      max_retries: 3
    };

    await this.taskAdapter.create({ data: task });

    // 创建步骤记录
    await this.createTaskSteps(taskId, strategy);

    return taskId;
  }

  /**
   * 创建任务步骤记录
   */
  private async createTaskSteps(taskId: string, strategy: ArbitrageStrategy): Promise<void> {
    const steps = this.getStepsForStrategy(strategy);
    
    for (let i = 0; i < steps.length; i++) {
      const stepDetail: Omit<TaskDetailEntity, 'id'> = {
        task_id: taskId,
        step_name: steps[i],
        step_order: i + 1,
        status: i === 0 ? StepStatus.PENDING : StepStatus.PENDING
      };
      
      await this.taskDetailAdapter.create({ data: stepDetail });
    }
  }

  /**
   * 获取策略对应的步骤列表
   */
  private getStepsForStrategy(strategy: ArbitrageStrategy): ArbitrageStep[] {
    if (strategy === ArbitrageStrategy.CEX_TO_DEX) {
      return [
        ArbitrageStep.BUY_TOKEN,
        ArbitrageStep.WITHDRAW_TOKEN,
        ArbitrageStep.SWAP_TOKEN,
        ArbitrageStep.BRIDGE_TRANSFER,
        ArbitrageStep.UNWRAP_SOL,
        ArbitrageStep.TRANSFER_TO_CEX,
        ArbitrageStep.SELL_TOKEN
      ];
    } else {
      return [
        ArbitrageStep.BUY_TOKEN,
        ArbitrageStep.WITHDRAW_TOKEN,
        ArbitrageStep.BRIDGE_TRANSFER,
        ArbitrageStep.SWAP_TOKEN,
        ArbitrageStep.TRANSFER_TO_CEX,
        ArbitrageStep.SELL_TOKEN
      ];
    }
  }

  /**
   * 更新任务步骤
   */
  async updateTaskStep(
    taskId: string, 
    step: ArbitrageStep, 
    stepData?: any,
    error?: string
  ): Promise<void> {
    // 更新主任务的当前步骤
    const task = await this.taskAdapter.findByTaskId(taskId);
    if (!task) {
      throw new Error(`Task ${taskId} not found`);
    }

    const retryCount = error ? task.retry_count + 1 : task.retry_count;
    await this.taskAdapter.updateStep(taskId, step, retryCount, error);

    // 更新步骤详情
    if (error) {
      await this.taskDetailAdapter.updateStepStatus(taskId, task.current_step, StepStatus.FAILED, {
        error_message: error
      });
    } else {
      // 标记当前步骤为完成
      await this.taskDetailAdapter.updateStepStatus(taskId, task.current_step, StepStatus.COMPLETED, {
        execution_result: stepData
      });

      // 如果不是最后一步，标记下一步为运行中
      if (step !== ArbitrageStep.COMPLETED && step !== ArbitrageStep.FAILED) {
        await this.taskDetailAdapter.updateStepStatus(taskId, step, StepStatus.RUNNING);
      }
    }
  }

  /**
   * 获取指定策略的未完成任务
   */
  async findIncompleteTask(strategy: ArbitrageStrategy): Promise<TaskEntity | null> {
    const runningTasks = await this.taskAdapter.findRunningByStrategy(strategy);
    return runningTasks.length > 0 ? runningTasks[0]! : null;
  }

  /**
   * 获取任务详情（包含所有步骤）
   */
  async getTaskWithSteps(taskId: string): Promise<{
    task: TaskEntity;
    steps: TaskDetailEntity[];
  } | null> {
    const task = await this.taskAdapter.findByTaskId(taskId);
    if (!task) {
      return null;
    }

    const steps = await this.taskDetailAdapter.findByTaskId(taskId);
    return { task, steps };
  }

  /**
   * 标记任务完成
   */
  async completeTask(taskId: string, finalData?: any): Promise<void> {
    await this.taskAdapter.updateStep(taskId, ArbitrageStep.COMPLETED);
    
    // 更新最后一个步骤为完成
    const task = await this.taskAdapter.findByTaskId(taskId);
    if (task) {
      await this.taskDetailAdapter.updateStepStatus(
        taskId, 
        task.current_step, 
        StepStatus.COMPLETED,
        { execution_result: finalData }
      );
    }
  }

  /**
   * 标记任务失败
   */
  async failTask(taskId: string, error: string): Promise<void> {
    await this.taskAdapter.updateStep(taskId, ArbitrageStep.FAILED, undefined, error);
    
    // 更新当前步骤为失败
    const task = await this.taskAdapter.findByTaskId(taskId);
    if (task) {
      await this.taskDetailAdapter.updateStepStatus(
        taskId, 
        task.current_step, 
        StepStatus.FAILED,
        { error_message: error }
      );
    }
  }
}
