{"name": "@unilab/urpc-core", "version": "0.0.15", "description": "Core types and utilities for the Unify RPC framework", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": "./dist/index.js", "./middleware": "./dist/middleware/index.js", "./builtin-plugin-entities": "./dist/builtin-plugin/entities/index.js"}, "publishConfig": {"access": "public"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "bun test", "prepublishOnly": "bun run build"}, "keywords": ["unify", "urpc", "core", "types", "utilities", "rpc"], "author": "", "license": "MIT", "dependencies": {"reflect-metadata": "^0.2.2"}, "devDependencies": {"@types/node": "^20.10.0", "typescript": "^5.3.0"}, "files": ["dist", "README.md"], "repository": {"type": "git", "url": "git+https://github.com/unifi-lab/unify.git", "directory": "packages/core"}}