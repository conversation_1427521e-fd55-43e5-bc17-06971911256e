import { Repository } from "./repository";
const REPO_REGISTRY = new Map();
export function registerAdapter(entity, source, adapter) {
    const key = getRepoKey(entity, source);
    const repo = new Repository(adapter);
    REPO_REGISTRY.set(key, repo);
    return repo;
}
export function getRepo(entity, source) {
    const key = getRepoKey(entity, source);
    const repo = REPO_REGISTRY.get(key);
    return repo;
}
export function getRepoRegistry() {
    return REPO_REGISTRY;
}
function getRepoKey(entity, source) {
    const simplifiedEntityName = simplifyEntityName(entity);
    return `${simplifiedEntityName}:${source}`;
}
export function simplifyEntityName(entity) {
    return entity.replace(/Entity$/i, "").toLowerCase();
}
