import "reflect-metadata";
export interface SchemaObject {
    type?: string | string[];
    properties?: Record<string, SchemaObject>;
    items?: SchemaObject;
    required?: string[];
    description?: string;
    format?: string;
    enum?: any[];
    default?: any;
    example?: any;
    nullable?: boolean;
    readOnly?: boolean;
    writeOnly?: boolean;
    deprecated?: boolean;
    allOf?: SchemaObject[];
    oneOf?: SchemaObject[];
    anyOf?: SchemaObject[];
    not?: SchemaObject;
    additionalProperties?: boolean | SchemaObject;
    minLength?: number;
    maxLength?: number;
    minimum?: number;
    maximum?: number;
    exclusiveMinimum?: boolean;
    exclusiveMaximum?: boolean;
    multipleOf?: number;
    minItems?: number;
    maxItems?: number;
    uniqueItems?: boolean;
    minProperties?: number;
    maxProperties?: number;
    pattern?: string;
}
export interface FieldMetadata {
    type: "string" | "number" | "boolean" | "date" | "array" | "record" | "action";
    optional?: boolean;
    description?: string;
    target?: () => any;
    params?: Record<string, any>;
    returns?: any;
}
export declare const Fields: {
    string: (options?: {
        optional?: boolean;
        description?: string;
    }) => (target: any, propertyKey: string) => void;
    number: (options?: {
        optional?: boolean;
        description?: string;
    }) => (target: any, propertyKey: string) => void;
    boolean: (options?: {
        optional?: boolean;
        description?: string;
    }) => (target: any, propertyKey: string) => void;
    date: (options?: {
        optional?: boolean;
        description?: string;
    }) => (target: any, propertyKey: string) => void;
    array: (target: () => any, options?: {
        optional?: boolean;
        description?: string;
    }) => (targetClass: any, propertyKey: string) => void;
    record: (target: () => any, options?: {
        optional?: boolean;
        description?: string;
    }) => (targetClass: any, propertyKey: string) => void;
    action: (options: {
        name: string;
        description?: string;
        params?: Record<string, any>;
        returns?: any;
    }) => (target: any, propertyKey: string) => void;
};
export declare function generateSchema(entityClass: any): SchemaObject;
export declare function generateSchemas(entityClasses: any[]): Record<string, SchemaObject>;
export declare function extractEntityClassName(entityClass: any): string;
export declare function extractAdapterName(adapterClass: any): string;
