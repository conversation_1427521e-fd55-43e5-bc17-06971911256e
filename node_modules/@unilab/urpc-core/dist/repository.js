import { <PERSON>rror<PERSON><PERSON>, URPCError } from "./error";
import { getMiddlewareManager } from "./middleware-manager";
export class Repository {
    constructor(adapter) {
        this.adapter = adapter;
    }
    async findMany(args, metadata) {
        return getMiddlewareManager().execute({
            args,
            operation: "findMany",
            metadata: metadata,
        }, async (ctx) => {
            return this.adapter.findMany(args, ctx);
        });
    }
    async findOne(args, metadata) {
        return getMiddlewareManager().execute({
            args,
            operation: "findOne",
            metadata: metadata,
        }, async (ctx) => {
            return this.adapter.findOne(args, ctx);
        });
    }
    async create(args, metadata) {
        return getMiddlewareManager().execute({
            args,
            operation: "create",
            metadata: metadata,
        }, async (ctx) => {
            const result = await this.adapter.create(args, ctx);
            return result;
        });
    }
    async createMany(args, metadata) {
        return getMiddlewareManager().execute({
            args,
            operation: "createMany",
            metadata: metadata,
        }, async (ctx) => {
            return this.adapter.createMany(args, ctx);
        });
    }
    async update(args, metadata) {
        return getMiddlewareManager().execute({
            args,
            operation: "update",
            metadata: metadata,
        }, async (ctx) => {
            return this.adapter.update(args, ctx);
        });
    }
    async updateMany(args, metadata) {
        return getMiddlewareManager().execute({
            args,
            operation: "updateMany",
            metadata: metadata,
        }, async (ctx) => {
            return this.adapter.updateMany(args, ctx);
        });
    }
    async upsert(args, metadata) {
        return getMiddlewareManager().execute({
            args,
            operation: "upsert",
            metadata: metadata,
        }, async (ctx) => {
            return this.adapter.upsert(args, ctx);
        });
    }
    async upsertMany(args, metadata) {
        return getMiddlewareManager().execute({
            args,
            operation: "upsertMany",
            metadata: metadata,
        }, async (ctx) => {
            return this.adapter.upsertMany(args, ctx);
        });
    }
    async delete(args, metadata) {
        return getMiddlewareManager().execute({
            args,
            operation: "delete",
            metadata: metadata,
        }, async (ctx) => {
            const result = await this.adapter.delete(args, ctx);
            return result;
        });
    }
    async customMethod(methodName, args, metadata) {
        if (!this.adapter[methodName]) {
            throw new URPCError(ErrorCodes.NOT_FOUND, "Method not implemented.");
        }
        return getMiddlewareManager().execute({
            args,
            operation: methodName,
            metadata: metadata,
        }, async (ctx) => {
            return this.adapter[methodName](args, ctx);
        });
    }
}
