import type { CreationArgs, CreateManyArgs, DataSourceAdapter, DeletionArgs, FindManyArgs, FindOneArgs, UpdateArgs, UpdateManyArgs, UpsertArgs, MiddlewareMetadata, UpsertManyArgs } from "./types";
export declare class Repository<T extends Record<string, any>> {
    private adapter;
    constructor(adapter: DataSourceAdapter<T>);
    findMany(args?: FindManyArgs<T>, metadata?: MiddlewareMetadata): Promise<T[]>;
    findOne(args: FindOneArgs<T>, metadata?: MiddlewareMetadata): Promise<T | null>;
    create(args: CreationArgs<T>, metadata?: MiddlewareMetadata): Promise<T>;
    createMany(args: CreateManyArgs<T>, metadata?: MiddlewareMetadata): Promise<T[]>;
    update(args: UpdateArgs<T>, metadata?: MiddlewareMetadata): Promise<T>;
    updateMany(args: UpdateManyArgs<T>, metadata?: MiddlewareMetadata): Promise<T[]>;
    upsert(args: UpsertArgs<T>, metadata?: MiddlewareMetadata): Promise<T>;
    upsertMany(args: UpsertManyArgs<T>, metadata?: MiddlewareMetadata): Promise<T[]>;
    delete(args: DeletionArgs<T>, metadata?: MiddlewareMetadata): Promise<boolean>;
    customMethod(methodName: string, args: any, metadata?: MiddlewareMetadata): Promise<any>;
}
