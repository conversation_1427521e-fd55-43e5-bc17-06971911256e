import { URPCError, <PERSON><PERSON>rC<PERSON> } from "./error";
export class BaseAdapter {
    async findMany(args, ctx) {
        throw new URPCError(ErrorCodes.NOT_FOUND, "Method not implemented.");
    }
    async findOne(args, ctx) {
        throw new URPCError(ErrorCodes.NOT_FOUND, "Method not implemented.");
    }
    async create(args, ctx) {
        throw new URPCError(ErrorCodes.NOT_FOUND, "Method not implemented.");
    }
    async createMany(args, ctx) {
        throw new URPCError(ErrorCodes.NOT_FOUND, "Method not implemented.");
    }
    async update(args, ctx) {
        throw new URPCError(ErrorCodes.NOT_FOUND, "Method not implemented.");
    }
    async updateMany(args, ctx) {
        throw new URPCError(ErrorCodes.NOT_FOUND, "Method not implemented.");
    }
    async upsert(args, ctx) {
        throw new URPCError(ErrorCodes.NOT_FOUND, "Method not implemented.");
    }
    async upsertMany(args, ctx) {
        throw new URPCError(ErrorCodes.NOT_FOUND, "Method not implemented.");
    }
    async delete(args, ctx) {
        throw new URPCError(ErrorCodes.NOT_FOUND, "Method not implemented.");
    }
}
