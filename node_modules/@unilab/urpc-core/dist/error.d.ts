export interface ErrorResponse {
    code: number;
    message: string;
}
export declare class URPCError extends Error implements ErrorResponse {
    name: string;
    code: number;
    message: string;
    constructor(code: number, message: string);
}
export declare const ErrorCodes: {
    readonly NOT_FOUND: 404;
    readonly BAD_REQUEST: 400;
    readonly UNAUTHORIZED: 401;
    readonly FORBIDDEN: 403;
    readonly INTERNAL_SERVER_ERROR: 500;
};
