import { Repository } from "./repository";
import { DataSourceAdapter } from "./types";
export declare function registerAdapter<T extends Record<string, any>>(entity: string, source: string, adapter: DataSourceAdapter<T>): Repository<T>;
export declare function getRepo(entity: string, source: string): Repository<any> | undefined;
export declare function getRepoRegistry(): Map<string, Repository<any>>;
export declare function simplifyEntityName(entity: string): string;
