import { BaseAdapter } from "../../adapter";
export class SchemaAdapter extends BaseAdapter {
    constructor(urpc) {
        super();
        this.urpc = urpc;
    }
    getSourcesForEntity(entity) {
        const entitySources = this.urpc.getEntitySources();
        return entitySources[entity] || [];
    }
    async findMany(args) {
        const where = args?.where || {};
        const schemas = this.urpc.getEntitySchemas();
        const entity = where.name;
        if (entity) {
            const actualEntityName = typeof entity === "string" ? entity : entity.$eq;
            if (actualEntityName) {
                const schema = schemas[actualEntityName];
                if (schema) {
                    return [
                        {
                            name: actualEntityName,
                            schema: schemas[actualEntityName],
                            sources: this.getSourcesForEntity(actualEntityName),
                        },
                    ];
                }
                else {
                    return [];
                }
            }
        }
        return Object.keys(schemas).map((name) => {
            return {
                name,
                schema: schemas[name],
                sources: this.getSourcesForEntity(name),
            };
        });
    }
    async findOne(args) {
        const where = args.where;
        const schemas = this.urpc.getEntitySchemas();
        const entityName = where.name;
        if (entityName) {
            if (entityName && schemas[entityName]) {
                return {
                    name: entityName,
                    schema: schemas[entityName],
                    sources: this.getSourcesForEntity(entityName),
                };
            }
        }
        return null;
    }
}
SchemaAdapter.displayName = "SchemaAdapter";
