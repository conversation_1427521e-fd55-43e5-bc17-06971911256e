import { BaseAdapter } from "../../adapter";
import { FindManyArgs, FindOneArgs } from "../../types";
import { SchemaEntity } from "../entities/schema";
import { URPC } from "../type";
export declare class SchemaAdapter extends BaseAdapter<SchemaEntity> {
    static displayName: string;
    private urpc;
    constructor(urpc: URPC);
    private getSourcesForEntity;
    findMany(args?: FindManyArgs<SchemaEntity>): Promise<SchemaEntity[]>;
    findOne(args: FindOneArgs<SchemaEntity>): Promise<SchemaEntity | null>;
}
