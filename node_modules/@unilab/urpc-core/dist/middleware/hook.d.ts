import type { Middleware, MiddlewareContext } from "../types";
export type HookFunction = (context: MiddlewareContext) => Promise<void> | void;
export interface HookRegistry {
    beforeCreate: HookFunction[];
    afterCreate: HookFunction[];
    beforeUpdate: HookFunction[];
    afterUpdate: HookFunction[];
    beforeDelete: HookFunction[];
    afterDelete: HookFunction[];
    beforeAny: HookFunction[];
    afterAny: HookFunction[];
}
export declare class HookManager {
    private hooks;
    beforeCreate(hook: HookFunction): this;
    afterCreate(hook: HookFunction): this;
    beforeUpdate(hook: HookFunction): this;
    afterUpdate(hook: HookFunction): this;
    beforeDelete(hook: HookFunction): this;
    afterDelete(hook: HookFunction): this;
    beforeAny(hook: HookFunction): this;
    afterAny(hook: HookFunction): this;
    executeBefore(context: MiddlewareContext): Promise<void>;
    executeAfter(context: MiddlewareContext): Promise<void>;
    clear(): void;
    clearType(type: keyof HookRegistry): void;
}
export declare function createHookMiddleware(setupHooks?: (hookManager: HookManager) => void): Middleware;
