export class HookManager {
    constructor() {
        this.hooks = {
            beforeCreate: [],
            afterCreate: [],
            beforeUpdate: [],
            afterUpdate: [],
            beforeDelete: [],
            afterDelete: [],
            beforeAny: [],
            afterAny: [],
        };
    }
    beforeCreate(hook) {
        this.hooks.beforeCreate.push(hook);
        return this;
    }
    afterCreate(hook) {
        this.hooks.afterCreate.push(hook);
        return this;
    }
    beforeUpdate(hook) {
        this.hooks.beforeUpdate.push(hook);
        return this;
    }
    afterUpdate(hook) {
        this.hooks.afterUpdate.push(hook);
        return this;
    }
    beforeDelete(hook) {
        this.hooks.beforeDelete.push(hook);
        return this;
    }
    afterDelete(hook) {
        this.hooks.afterDelete.push(hook);
        return this;
    }
    beforeAny(hook) {
        this.hooks.beforeAny.push(hook);
        return this;
    }
    afterAny(hook) {
        this.hooks.afterAny.push(hook);
        return this;
    }
    async executeBefore(context) {
        for (const hook of this.hooks.beforeAny) {
            await hook(context);
        }
        switch (context.operation) {
            case "create":
                for (const hook of this.hooks.beforeCreate) {
                    await hook(context);
                }
                break;
            case "update":
                for (const hook of this.hooks.beforeUpdate) {
                    await hook(context);
                }
                break;
            case "delete":
                for (const hook of this.hooks.beforeDelete) {
                    await hook(context);
                }
                break;
        }
    }
    async executeAfter(context) {
        switch (context.operation) {
            case "create":
                for (const hook of this.hooks.afterCreate) {
                    await hook(context);
                }
                break;
            case "update":
                for (const hook of this.hooks.afterUpdate) {
                    await hook(context);
                }
                break;
            case "delete":
                for (const hook of this.hooks.afterDelete) {
                    await hook(context);
                }
                break;
        }
        for (const hook of this.hooks.afterAny) {
            await hook(context);
        }
    }
    clear() {
        this.hooks = {
            beforeCreate: [],
            afterCreate: [],
            beforeUpdate: [],
            afterUpdate: [],
            beforeDelete: [],
            afterDelete: [],
            beforeAny: [],
            afterAny: [],
        };
    }
    clearType(type) {
        this.hooks[type] = [];
    }
}
export function createHookMiddleware(setupHooks) {
    const hookManager = new HookManager();
    if (setupHooks) {
        setupHooks(hookManager);
    }
    const fn = async (context, next) => {
        await hookManager.executeBefore(context);
        const result = await next();
        context.result = result;
        await hookManager.executeAfter(context);
        return result;
    };
    return {
        fn,
        name: "HookMiddleware",
    };
}
