import { SchemaObject } from "./decorators";
import { BaseURPCConfig, EntityConfigs } from "./types";
export declare class BaseURPC {
    static entitySchemas: Record<string, SchemaObject>;
    static entitySources: Record<string, string[]>;
    static entityConfigs: EntityConfigs;
    static init(config: BaseURPCConfig): void;
    private static registerPluginAdapters;
    private static registerGlobalAdapters;
    private static applyMiddlewareToRepos;
    private static analyzeEntities;
    static getEntitySchemas(): Record<string, SchemaObject>;
    static getEntitySources(): Record<string, string[]>;
    static getEntityConfigs(): EntityConfigs;
}
