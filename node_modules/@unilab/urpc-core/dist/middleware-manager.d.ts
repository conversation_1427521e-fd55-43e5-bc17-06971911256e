import { EntityConfigs, Middleware, MiddlewareContext, MiddlewareManagerInterface, MiddlewareOptions, OperationContext } from "./types";
declare class MiddlewareManager implements MiddlewareManagerInterface {
    entityConfigs: EntityConfigs;
    private middlewares;
    setEntityConfigs(entityConfigs: EntityConfigs): void;
    use(middleware: Middleware, options?: MiddlewareOptions): void;
    remove(name: string): boolean;
    clear(): void;
    execute(context: MiddlewareContext, operation: (ctx: OperationContext) => Promise<any>): Promise<any>;
}
export declare function getMiddlewareManager(): MiddlewareManager;
export {};
