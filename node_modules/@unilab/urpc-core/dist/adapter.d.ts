import { CreationArgs, CreateManyArgs, DataSourceAdapter, DeletionArgs, FindManyArgs, FindOneArgs, UpdateArgs, UpdateManyArgs, UpsertArgs, OperationContext, UpsertManyArgs } from "./types";
export declare class BaseAdapter<T extends Record<string, any>> implements DataSourceAdapter<T> {
    findMany(args?: FindManyArgs<T>, ctx?: OperationContext): Promise<T[]>;
    findOne(args: FindOneArgs<T>, ctx?: OperationContext): Promise<T | null>;
    create(args: CreationArgs<T>, ctx?: OperationContext): Promise<T>;
    createMany(args: CreateManyArgs<T>, ctx?: OperationContext): Promise<T[]>;
    update(args: UpdateArgs<T>, ctx?: OperationContext): Promise<T>;
    updateMany(args: UpdateManyArgs<T>, ctx?: OperationContext): Promise<T[]>;
    upsert(args: UpsertArgs<T>, ctx?: OperationContext): Promise<T>;
    upsertMany(args: UpsertManyArgs<T>, ctx?: OperationContext): Promise<T[]>;
    delete(args: DeletionArgs<T>, ctx?: OperationContext): Promise<boolean>;
    [methodName: string]: any;
}
