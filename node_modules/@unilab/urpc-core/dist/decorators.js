import "reflect-metadata";
// Metadata keys for reflect-metadata
const FIELD_METADATA_KEY = Symbol("field:metadata");
export const Fields = {
    string: (options) => {
        const decorator = function stringFieldDecorator(target, propertyKey) {
            setFieldMetadata(target.constructor, propertyKey, {
                type: "string",
                optional: options?.optional,
                description: options?.description,
            });
        };
        Object.defineProperty(decorator, "name", { value: "stringFieldDecorator" });
        return decorator;
    },
    number: (options) => {
        const decorator = function numberFieldDecorator(target, propertyKey) {
            setFieldMetadata(target.constructor, propertyKey, {
                type: "number",
                optional: options?.optional,
                description: options?.description,
            });
        };
        Object.defineProperty(decorator, "name", { value: "numberFieldDecorator" });
        return decorator;
    },
    boolean: (options) => {
        const decorator = function booleanFieldDecorator(target, propertyKey) {
            setFieldMetadata(target.constructor, property<PERSON>ey, {
                type: "boolean",
                optional: options?.optional,
                description: options?.description,
            });
        };
        Object.defineProperty(decorator, "name", {
            value: "booleanFieldDecorator",
        });
        return decorator;
    },
    date: (options) => {
        const decorator = function dateFieldDecorator(target, propertyKey) {
            setFieldMetadata(target.constructor, propertyKey, {
                type: "date",
                optional: options?.optional,
                description: options?.description,
            });
        };
        Object.defineProperty(decorator, "name", { value: "dateFieldDecorator" });
        return decorator;
    },
    array: (target, options) => {
        const decorator = function arrayFieldDecorator(targetClass, propertyKey) {
            setFieldMetadata(targetClass.constructor, propertyKey, {
                type: "array",
                target,
                optional: options?.optional,
                description: options?.description,
            });
        };
        Object.defineProperty(decorator, "name", { value: "arrayFieldDecorator" });
        return decorator;
    },
    record: (target, options) => {
        const decorator = function recordFieldDecorator(targetClass, propertyKey) {
            setFieldMetadata(targetClass.constructor, propertyKey, {
                type: "record",
                target,
                optional: options?.optional,
                description: options?.description,
            });
        };
        Object.defineProperty(decorator, "name", { value: "recordFieldDecorator" });
        return decorator;
    },
    action: (options) => {
        const decorator = function actionFieldDecorator(target, propertyKey) {
            setFieldMetadata(target.constructor, propertyKey, {
                type: "action",
                description: options.description,
                params: options.params,
                returns: options.returns,
            });
        };
        Object.defineProperty(decorator, "name", { value: "actionFieldDecorator" });
        return decorator;
    },
};
// Metadata helper functions using reflect-metadata
function setFieldMetadata(target, propertyKey, metadata) {
    const existingMetadata = Reflect.getMetadata(FIELD_METADATA_KEY, target) || {};
    existingMetadata[propertyKey] = metadata;
    Reflect.defineMetadata(FIELD_METADATA_KEY, existingMetadata, target);
}
function getFieldMetadata(target, propertyKey) {
    const metadata = Reflect.getMetadata(FIELD_METADATA_KEY, target);
    return metadata?.[propertyKey];
}
function getAllFieldMetadata(target) {
    return Reflect.getMetadata(FIELD_METADATA_KEY, target) || {};
}
// Helper function to get entity class name from target function
function getEntityTypeName(targetFunction) {
    try {
        const targetClass = targetFunction();
        return extractEntityClassName(targetClass);
    }
    catch (error) {
        // If target function fails (e.g., circular dependency), try to extract from function string
        const funcStr = targetFunction.toString();
        const match = funcStr.match(/return\s+(?:require\([^)]+\)\.)?(\w+)/);
        return match ? match[1] : "UnknownEntity";
    }
}
// Schema generation using custom SchemaObject interface
export function generateSchema(entityClass) {
    const fields = getAllFieldMetadata(entityClass);
    const properties = {};
    const required = [];
    for (const [propertyKey, metadata] of Object.entries(fields)) {
        let property;
        if (metadata.type === "array" && metadata.target) {
            const relatedEntityName = getEntityTypeName(metadata.target);
            property = {
                type: "array",
                items: {
                    type: relatedEntityName,
                },
                description: metadata.description,
            };
        }
        else if (metadata.type === "record" && metadata.target) {
            const relatedEntityName = getEntityTypeName(metadata.target);
            property = {
                type: relatedEntityName,
                description: metadata.description,
            };
        }
        else {
            property = {
                type: metadata.type === "date" ? "string" : metadata.type,
                description: metadata.description,
            };
            if (metadata.type === "array" && !metadata.target) {
                property.type = "array";
                property.items = { type: "string" };
            }
        }
        properties[propertyKey] = property;
        if (!metadata.optional) {
            required.push(propertyKey);
        }
    }
    return {
        type: "object",
        properties,
        required,
    };
}
export function generateSchemas(entityClasses) {
    const schemas = {};
    for (const entityClass of entityClasses) {
        const name = extractEntityClassName(entityClass);
        schemas[name] = generateSchema(entityClass);
    }
    return schemas;
}
export function extractEntityClassName(entityClass) {
    return entityClass.displayName || entityClass.name;
}
export function extractAdapterName(adapterClass) {
    return adapterClass.displayName || adapterClass.name;
}
