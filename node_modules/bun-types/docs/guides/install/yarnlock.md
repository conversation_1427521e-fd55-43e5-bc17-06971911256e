---
name: Generate a yarn-compatible lockfile
---

{% callout %}
Bun v1.1.39 introduced `bun.lock`, a JSONC formatted lockfile. `bun.lock` is human-readable and git-diffable without configuration, at no cost to performance. [**Learn more.**](https://bun.com/docs/install/lockfile#text-based-lockfile)
{% /callout %}

---

Use the `--yarn` flag to generate a Yarn-compatible `yarn.lock` file (in addition to `bun.lock`).

```sh
$ bun install --yarn
```

---

To set this as the default behavior, add the following to your `bunfig.toml` file.

```toml#bunfig.toml
[install.lockfile]
print = "yarn"
```

---

To print a Yarn lockfile to your console without writing it to disk, "run" your `bun.lockb` with `bun`.

```sh
$ bun bun.lockb
# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1
# bun ./bun.lockb --hash: 9BFBF11D86084AAB-9418b03ff880c569-390CE6459EACEC9A...

abab@^2.0.6:
  version "2.0.6"
  resolved "https://registry.npmjs.org/abab/-/abab-2.0.6.tgz"
  integrity sha512-j2afSsaIENvHZN2B8GOpF566vZ5WVk5opAiMTvWgaQT8DkbOqsTfvNAvH...
```

---

See [Docs > Package manager](https://bun.com/docs/cli/install) for complete documentation of Bun's package manager.
