Bun implements the following globals.

{% table %}

- Global
- Source
- Notes

---

- [`AbortController`](https://developer.mozilla.org/en-US/docs/Web/API/AbortController)
- Web
- &nbsp;

---

- [`AbortSignal`](https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal)
- Web
- &nbsp;

---

- [`alert`](https://developer.mozilla.org/en-US/docs/Web/API/Window/alert)
- Web
- Intended for command-line tools

---

- [`Blob`](https://developer.mozilla.org/en-US/docs/Web/API/Blob)
- Web
- &nbsp;

---

- [`Buffer`](https://nodejs.org/api/buffer.html#class-buffer)
- Node.js
- See [Node.js > `Buffer`](https://bun.com/docs/runtime/nodejs-apis#node-buffer)

---

- `Bun`
- Bun
- Subject to change as additional APIs are added

---

- [`ByteLengthQueuingStrategy`](https://developer.mozilla.org/en-US/docs/Web/API/ByteLengthQueuingStrategy)
- Web
- &nbsp;

---

- [`confirm`](https://developer.mozilla.org/en-US/docs/Web/API/Window/confirm)
- Web
- Intended for command-line tools

---

- [`__dirname`](https://nodejs.org/api/globals.html#__dirname)
- Node.js
- &nbsp;

---

- [`__filename`](https://nodejs.org/api/globals.html#__filename)
- Node.js
- &nbsp;

---

- [`atob()`](https://developer.mozilla.org/en-US/docs/Web/API/atob)
- Web
- &nbsp;

---

- [`btoa()`](https://developer.mozilla.org/en-US/docs/Web/API/btoa)
- Web
- &nbsp;

---

- `BuildMessage`
- Bun
- &nbsp;

---

- [`clearImmediate()`](https://developer.mozilla.org/en-US/docs/Web/API/Window/clearImmediate)
- Web
- &nbsp;

---

- [`clearInterval()`](https://developer.mozilla.org/en-US/docs/Web/API/Window/clearInterval)
- Web
- &nbsp;

---

- [`clearTimeout()`](https://developer.mozilla.org/en-US/docs/Web/API/Window/clearTimeout)
- Web
- &nbsp;

---

- [`console`](https://developer.mozilla.org/en-US/docs/Web/API/console)
- Web
- &nbsp;

---

- [`CountQueuingStrategy`](https://developer.mozilla.org/en-US/docs/Web/API/CountQueuingStrategy)
- Web
- &nbsp;

---

- [`Crypto`](https://developer.mozilla.org/en-US/docs/Web/API/Crypto)
- Web
- &nbsp;

---

- [`crypto`](https://developer.mozilla.org/en-US/docs/Web/API/crypto)
- Web
- &nbsp;

---

- [`CryptoKey`](https://developer.mozilla.org/en-US/docs/Web/API/CryptoKey)
- Web
- &nbsp;

---

- [`CustomEvent`](https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent)
- Web
- &nbsp;

---

- [`Event`](https://developer.mozilla.org/en-US/docs/Web/API/Event)
- Web
- Also [`ErrorEvent`](https://developer.mozilla.org/en-US/docs/Web/API/ErrorEvent) [`CloseEvent`](https://developer.mozilla.org/en-US/docs/Web/API/CloseEvent) [`MessageEvent`](https://developer.mozilla.org/en-US/docs/Web/API/MessageEvent).

---

- [`EventTarget`](https://developer.mozilla.org/en-US/docs/Web/API/EventTarget)
- Web
- &nbsp;

---

- [`exports`](https://nodejs.org/api/globals.html#exports)
- Node.js
- &nbsp;

---

- [`fetch`](https://developer.mozilla.org/en-US/docs/Web/API/fetch)
- Web
- &nbsp;

---

- [`FormData`](https://developer.mozilla.org/en-US/docs/Web/API/FormData)
- Web
- &nbsp;

---

- [`global`](https://nodejs.org/api/globals.html#global)
- Node.js
- See [Node.js > `global`](https://bun.com/docs/runtime/nodejs-apis#global).

---

- [`globalThis`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/globalThis)
- Cross-platform
- Aliases to `global`

---

- [`Headers`](https://developer.mozilla.org/en-US/docs/Web/API/Headers)
- Web
- &nbsp;

---

- [`HTMLRewriter`](https://bun.com/docs/api/html-rewriter)
- Cloudflare
- &nbsp;

---

- [`JSON`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON)
- Web
- &nbsp;

---

- [`MessageEvent`](https://developer.mozilla.org/en-US/docs/Web/API/MessageEvent)
- Web
- &nbsp;

---

- [`module`](https://nodejs.org/api/globals.html#module)
- Node.js
- &nbsp;

---

- [`performance`](https://developer.mozilla.org/en-US/docs/Web/API/performance)
- Web
- &nbsp;

---

- [`process`](https://nodejs.org/api/process.html)
- Node.js
- See [Node.js > `process`](https://bun.com/docs/runtime/nodejs-apis#node-process)

---

- [`prompt`](https://developer.mozilla.org/en-US/docs/Web/API/Window/prompt)
- Web
- Intended for command-line tools

---

- [`queueMicrotask()`](https://developer.mozilla.org/en-US/docs/Web/API/queueMicrotask)
- Web
- &nbsp;

---

- [`ReadableByteStreamController`](https://developer.mozilla.org/en-US/docs/Web/API/ReadableByteStreamController)
- Web
- &nbsp;

---

- [`ReadableStream`](https://developer.mozilla.org/en-US/docs/Web/API/ReadableStream)
- Web
- &nbsp;

---

- [`ReadableStreamDefaultController`](https://developer.mozilla.org/en-US/docs/Web/API/ReadableStreamDefaultController)
- Web
- &nbsp;

---

- [`ReadableStreamDefaultReader`](https://developer.mozilla.org/en-US/docs/Web/API/ReadableStreamDefaultReader)
- Web
- &nbsp;

---

- [`reportError`](https://developer.mozilla.org/en-US/docs/Web/API/reportError)
- Web
- &nbsp;

---

- [`require()`](https://nodejs.org/api/globals.html#require)
- Node.js
- &nbsp;

---

- `ResolveMessage`
- Bun
- &nbsp;

---

- [`Response`](https://developer.mozilla.org/en-US/docs/Web/API/Response)
- Web
- &nbsp;

---

- [`Request`](https://developer.mozilla.org/en-US/docs/Web/API/Request)
- Web
- &nbsp;

---

- [`setImmediate()`](https://developer.mozilla.org/en-US/docs/Web/API/Window/setImmediate)
- Web
- &nbsp;

---

- [`setInterval()`](https://developer.mozilla.org/en-US/docs/Web/API/Window/setInterval)
- Web
- &nbsp;

---

- [`setTimeout()`](https://developer.mozilla.org/en-US/docs/Web/API/Window/setTimeout)
- Web
- &nbsp;

---

- [`ShadowRealm`](https://github.com/tc39/proposal-shadowrealm)
- Web
- Stage 3 proposal

---

- [`SubtleCrypto`](https://developer.mozilla.org/en-US/docs/Web/API/SubtleCrypto)
- Web
- &nbsp;

---

- [`DOMException`](https://developer.mozilla.org/en-US/docs/Web/API/DOMException)
- Web
- &nbsp;

---

- [`TextDecoder`](https://developer.mozilla.org/en-US/docs/Web/API/TextDecoder)
- Web
- &nbsp;

---

- [`TextEncoder`](https://developer.mozilla.org/en-US/docs/Web/API/TextEncoder)
- Web
- &nbsp;

---

- [`TransformStream`](https://developer.mozilla.org/en-US/docs/Web/API/TransformStream)
- Web
- &nbsp;

---

- [`TransformStreamDefaultController`](https://developer.mozilla.org/en-US/docs/Web/API/TransformStreamDefaultController)
- Web
- &nbsp;

---

- [`URL`](https://developer.mozilla.org/en-US/docs/Web/API/URL)
- Web
- &nbsp;

---

- [`URLSearchParams`](https://developer.mozilla.org/en-US/docs/Web/API/URLSearchParams)
- Web
- &nbsp;

---

- [`WebAssembly`](https://nodejs.org/api/globals.html#webassembly)
- Web
- &nbsp;

---

- [`WritableStream`](https://developer.mozilla.org/en-US/docs/Web/API/WritableStream)
- Web
- &nbsp;

---

- [`WritableStreamDefaultController`](https://developer.mozilla.org/en-US/docs/Web/API/WritableStreamDefaultController)
- Web
- &nbsp;

---

- [`WritableStreamDefaultWriter`](https://developer.mozilla.org/en-US/docs/Web/API/WritableStreamDefaultWriter)
- Web
- &nbsp;

{% /table %}
