Bun is a project with an incredibly large scope and is still in its early days. Long-term, <PERSON><PERSON> aims to provide an all-in-one toolkit to replace the complex, fragmented toolchains common today: Node.js, Jest, Webpack, esbuild, Babel, yarn, PostCSS, etc.

Refer to [Bun's Roadmap](https://github.com/oven-sh/bun/issues/159) on GitHub to learn more about the project's long-term plans and priorities.

<!--
{% table %}

- Feature
- Implemented in

---

- Web Streams with HTMLRewriter
- Bun.js

---

- Source Maps (unbundled is supported)
- JS Bundler

---

- Source Maps
- CSS

---

- JavaScript Minifier
- JS Transpiler

---

- CSS Minifier
- CSS

---

- CSS Parser (it only bundles)
- CSS

---

- Tree-shaking
- JavaScript

---

- Tree-shaking
- CSS

---

- [TypeScript Decorators](https://www.typescriptlang.org/docs/handbook/decorators.html)
- TS Transpiler

---

- `@jsxPragma` comments
- JS Transpiler

---

- Sharing `.bun` files
- Bun

---

- Dates & timestamps
- TOML parser

---

- [Hash components for Fast Refresh](https://github.com/oven-sh/bun/issues/18)
- JSX Transpiler

{% /table %} -->

<!-- ## Limitations & intended usage

Today, Bun is mostly focused on Bun.js: the JavaScript runtime.

While you could use Bun's bundler & transpiler separately to build for browsers or node, Bun doesn't have a minifier or support tree-shaking yet. For production browser builds, you probably should use a tool like esbuild or swc.

## Upcoming breaking changes

- Bun's CLI flags will change to better support Bun as a JavaScript runtime. They were chosen when Bun was just a frontend development tool.
- Bun's bundling format will change to accommodate production browser bundles and on-demand production bundling -->
